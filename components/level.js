async function setLevel(userId, level, task, amount, pool) {
    try {
        const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
        if (users.length === 0) {
            return;
        }
        const user = users[0];
        let userLevel = JSON.parse(user.level);
        const complete = userLevel[level, task].complete;

        if (complete == false) {
            userLevel[level, task].value += amount;

            const target = userLevel[level, task].target;
            const value = userLevel[level, task].value;

            if (target >= value) {
                userLevel[level, task].complete = true;
            }
        }

        await pool.query(
            `
        UPDATE users 
        SET level = ?
        WHERE id = ?
        `,
            [JSON.stringify(userLevel), userId]
        );
    } catch (e) {
        console.log(e);
        return;
    }

}

async function findUserLevel(userId, pool) {
    const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
    if (users.length === 0) {
        return 0;
    }
    const user = users[0];
    const levelData = JSON.parse(user.level);
    let userLevel = 0;

    for (let i = 0; i < levelData.length; i++) {
        const currentLevel = levelData[i];

        const completedTargets = currentLevel.filter(target => target.complete).length;

        if (completedTargets === 5) {
            userLevel = i + 1;
        } else {
            break;
        }
    }

    return userLevel;
}

module.exports = {
    setLevel,
    findUserLevel,
};