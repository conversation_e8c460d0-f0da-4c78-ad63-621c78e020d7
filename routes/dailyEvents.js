const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');

module.exports = (pool) => {
    router.get('/', verifyJWTToken, async (req, res) => {
        try {
            const [rows] = await pool.query('SELECT * FROM `concerts`');
            return res.status(200).json({ type: 'success', data: rows });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }

    });

    router.get('/detail', verifyJWTToken, async (req, res) => {
        const { id } = req.query;
        if (!id) {
            return res.status(200).json({ type: 'error', error: 'Konser bulunamadı!' });
        }
        try {
            const [dailyActivities] = await pool.query(
            `
            SELECT da.*, s.name as stageName
            FROM daily_activities da
            JOIN stages s ON s.id = da.stageId
            WHERE da.id = ?
            `, [id]
            );
            if (dailyActivities.length == 0) {
                return res.status(200).json({ type: 'error', error: 'Konser bulunamadı!' });
            }
            
            return res.status(200).json({ type: 'success', dailyActivity: dailyActivities[0] });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    return router;
};
