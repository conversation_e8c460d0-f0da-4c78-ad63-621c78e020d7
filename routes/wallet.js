const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const winston = require('winston');
const moment = require('moment');


require('dotenv').config();


const logger = winston.createLogger({
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/error.log' })
    ]
});

module.exports = (pool) => {
    router.post('/create-wallet', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;

            const [existingWallets] = await pool.query('SELECT id FROM wallets WHERE userId = ?', [userId]);

            if (existingWallets.length > 0) {
                return res.status(400).json({ type: 'error', error: 'Bu kullanıcı zaten bir cüzdana sahip.' });
            }

            const [result] = await pool.query('INSERT INTO wallets (userId, balance) VALUES (?, ?)', [userId, 0]);
            const newWalletId = result.insertId;

            await pool.query('UPDATE users SET walletId = ? WHERE id = ?', [newWalletId, userId]);

            res.status(200).json({ type: 'success', message: 'Cüzdan başarıyla oluşturuldu.', walletId: newWalletId });
        } catch (error) {
            logger.error('Hata:', error);
            res.status(500).json({ type: 'error', error: 'Bir hata oluştu, cüzdan oluşturulamadı.' });
        }
    })

    router.get('/contract', verifyJWTToken, async (req, res) => {
        try {
            const contractHTML = `
                <h3>Kullanıcı Sözleşmesi</h3>
                <p>İşbu sözleşme, kullanıcı ile uygulama arasında yapılmış olup, kullanıcı uygulamayı kullanmaya başladığı anda yürürlüğe girer.</p>
                <p>Kullanıcı, cüzdan oluşturduğunda aşağıdaki şartları kabul etmiş sayılır:</p>
                <ul>
                    <li>Kullanıcı bilgileri gizli tutulacaktır.</li>
                    <li>Uygulama içerisinde yapılan işlemler kullanıcı sorumluluğundadır.</li>
                    <li>Cashback oranı %5 olarak belirlenmiştir ve değiştirilebilir.</li>
                    <li>İşlemler sırasında kullanılan puanlar iade edilemez.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                    <li>Diğer şartlar ve koşullar için destek birimiyle iletişime geçebilirsiniz.</li>
                </ul>
            `;
            res.status(200).json({ type: 'success', contractHTML });
        } catch (error) {
            logger.error('Hata:', error);
            res.status(500).json({ type: 'error', message: 'Bir hata oluştu, sözleşme yüklenemedi.' });
        }
    });



    router.get('/check-wallet', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;

            const [walletData] = await pool.query('SELECT id, balance FROM wallets WHERE userId = ?', [userId]);

            if (walletData.length > 0) {
                return res.status(200).json({ type: 'success', walletInfo: { id: walletData[0].id, balance: walletData[0].balance } });
            } else {

                return res.status(200).json({ type: 'info', message: 'Kullanıcının bir cüzdanı bulunmamaktadır.' });
            }
        } catch (error) {
            logger.error('Hata:', error);
            res.status(500).json({ type: 'error', error: 'Bir hata oluştu, cüzdan bilgileri alınamadı.' });
        }
    });

    router.post('/pay', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;
            const { amount, qrCodeContent } = req.body;

            // Kullanıcının bakiyesini kontrol et
            const [walletData] = await pool.query('SELECT id, balance FROM wallets WHERE userId = ?', [userId]);

            if (walletData.length === 0) {
                logger.error('Kullanıcının cüzdanı bulunmamaktadır.');
                return res.status(200).json({ type: 'error', message: 'Kullanıcının cüzdanı bulunmamaktadır.' });
            }

            const walletId = walletData[0].id;
            const currentBalance = parseFloat(walletData[0].balance); // Bakiye sayıya dönüştürülüyor
            const requestedAmount = parseFloat(amount); // İstek miktarı sayıya dönüştürülüyor

            if (isNaN(currentBalance) || isNaN(requestedAmount)) {
                logger.error('Geçersiz bakiye veya miktar formatı.');
                return res.status(200).json({ type: 'error', message: 'Geçersiz bakiye veya miktar formatı.' });
            }

            if (currentBalance < requestedAmount) {
                logger.error('Yetersiz bakiye.');
                return res.status(200).json({ type: 'error', message: 'Yetersiz bakiye.' });
            }



            // Cashback miktarını hesapla
            const cashbackPercentage = 0.05; // %5 cashback oranı
            const cashbackAmount = requestedAmount * cashbackPercentage;

            // Türkiye saatiyle tarih oluştur
            const turkiyeSaatiyleTarih = moment().utcOffset('+0300').format("YYYY-MM-DD HH:mm:ss");

            // Transaction tablosuna veriyi kaydet
            const transactionData = {
                walletId,
                type: 'expense',
                amount: requestedAmount, // İstek miktarı kullanılıyor
                description: qrCodeContent, // İsim, soyisim ve QR kodu içeriğini içeren açıklama
                createdAt: turkiyeSaatiyleTarih,
                cashback: cashbackAmount, // Cashback miktarı
                usedHollyPoints: '0', // Kullanılan Holly Points miktarı
                kasaonay: '0',
                userId: userId,
            };

            // Transaction kaydını oluştur ve ID'sini al
            const [insertResult] = await pool.query('INSERT INTO transactions SET ?', [transactionData]);
            const transactionId = insertResult.insertId;

            // Kullanıcının bakiyesini güncelle
            let updatedBalance = currentBalance - requestedAmount; // İstek miktarı kullanılıyor


            await pool.query('UPDATE wallets SET balance = ? WHERE id = ?', [updatedBalance, walletId]);

            // Diğer API'ye istek gönder
            try {
                const axios = require('axios');

                // Kullanıcı bilgilerini al
                const [userData] = await pool.query('SELECT firstName, lastName, email, phoneNumber FROM users WHERE id = ?', [userId]);
                const user = userData[0];

                // QR ödeme işlemi için diğer API'ye istek gönder
                const qrPaymentUrl = process.env.QR_PAYMENT_PROCESS_URL;

                logger.info(`QR ödeme işlemi için istek gönderiliyor: ${qrPaymentUrl}`);
                logger.info(`İstek parametreleri: amount=${amount}, qrCodeContent=${qrCodeContent}`);

                const response = await axios.post(qrPaymentUrl, {
                    amount: requestedAmount,
                    qrCodeContent: qrCodeContent,
                    transactionId: transactionId,
                    userId: userId,
                    userInfo: {
                        name: `${user.firstName} ${user.lastName}`,
                        email: user.email,
                        phone: user.phoneNumber
                    },
                    timestamp: turkiyeSaatiyleTarih
                });

                logger.info('QR ödeme API yanıtı:', response.data);

                // Eğer API yanıtı başarılı değilse, işlemi geri al
                if (!response.data.success) {
                    logger.error('QR ödeme API hatası:', response.data);

                    // Bakiyeyi geri yükle
                    await pool.query('UPDATE wallets SET balance = ? WHERE id = ?', [currentBalance, walletId]);


                    // Transaction durumunu güncelle
                    await pool.query('UPDATE transactions SET status = ? WHERE id = ?', ['failed', transactionId]);

                    return res.status(200).json({
                        type: 'error',
                        message: 'QR ödeme işlemi başarısız oldu.',
                        details: response.data.message || 'Diğer sistemde bir hata oluştu.'
                    });
                }

        
                return res.status(200).json({
                    type: 'success',
                    message: 'İşlem başarıyla tamamlandı.',
                    transactionId: transactionId,
                    referenceId: response.data.referenceId || null
                });

            } catch (apiError) {
                logger.error('QR ödeme API hatası:', apiError);

                // Bakiyeyi geri yükle
                await pool.query('UPDATE wallets SET balance = ? WHERE id = ?', [currentBalance, walletId]);


                // Transaction durumunu güncelle
                await pool.query('UPDATE transactions SET status = ? WHERE id = ?', ['failed', transactionId]);

                return res.status(200).json({
                    type: 'error',
                    message: 'QR ödeme işlemi sırasında bir hata oluştu.',
                    details: apiError.message
                });
            }

        } catch (error) {
            logger.error('Hata:', error);
            res.status(500).json({ type: 'error', message: 'Bir hata oluştu, işlem tamamlanamadı.' });
        }
    });




    router.get('/transactions', verifyJWTToken, async (req, res) => {
        try {
            const userId = req.decodedToken.userId;

            // Kullanıcının cüzdanını al
            const [walletData] = await pool.query('SELECT id FROM wallets WHERE userId = ?', [userId]);

            // Kullanıcının cüzdanı bulunamazsa hata döndür
            if (walletData.length === 0) {
                return res.status(404).json({ type: 'error', message: 'Kullanıcının cüzdanı bulunamadı.' });
            }

            const walletId = walletData[0].id;

            // Cüzdan ID'sine göre işlemleri al
            const [transactions] = await pool.query('SELECT * FROM transactions WHERE walletId = ?', [walletId]);

            res.status(200).json({ type: 'success', transactions });
        } catch (error) {
            logger.error('Hata:', error);
            res.status(500).json({ type: 'error', message: 'Bir hata oluştu, işlem tamamlanamadı.' });
        }
    });




    return router;
};
