const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');

module.exports = (pool) => {
  router.get('/', verifyJWTToken, async (req, res) => {
    const userId = req.decodedToken.userId;
    try {
      // -- CHECK USER -- //
      const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
      if (users.length === 0) {
        return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
      }

      // ***** HAFTALIK ÖDÜL *****
      const [shamanPrizesWeek] = await pool.query(`
        SELECT * 
        FROM shaman_prize 
        WHERE type = 'week' 
          AND startDate <= CURDATE() 
          AND endDate >= CURDATE() 
        ORDER BY startDate DESC 
        LIMIT 1;
      `);
      let weekData = null;
      if (shamanPrizesWeek.length === 0) {
        weekData = { error: 'Ödül bulunamadı!' };
      } else {
        const shamanPrize = shamanPrizesWeek[0];


        const [leaderBoardWeek] = await pool.query(`
          WITH earnings AS (
            SELECT 
              e.userId, 
              SUM(e.hollyPoints) AS total_earnings 
            FROM 
              holly_points_earning_history e 
              JOIN shaman_prize sp ON e.createdAt BETWEEN sp.startDate AND sp.endDate 
            WHERE 
              sp.type = 'week'
              AND sp.startDate <= CURDATE() 
              AND sp.endDate >= CURDATE()
            GROUP BY 
              e.userId
          ), 
          spending AS (
            SELECT 
              s.userId, 
              SUM(s.hollyPoints) AS total_spending 
            FROM 
              holly_points_spend_history s 
              JOIN shaman_prize sp ON s.createdAt BETWEEN sp.startDate AND sp.endDate 
            WHERE 
              sp.type = 'week'
              AND sp.startDate <= CURDATE() 
              AND sp.endDate >= CURDATE()
            GROUP BY 
              s.userId
          ) 
          SELECT 
            u.id AS userId,
            CONCAT(u.firstName, ' ', u.lastName) AS nameLastName,
            u.levelNumber AS level,
            u.image, 
            u.status,
            COALESCE(e.total_earnings, 0) - COALESCE(s.total_spending, 0) AS hollyPoints 
          FROM 
            users u 
            LEFT JOIN earnings e ON u.id = e.userId 
            LEFT JOIN spending s ON u.id = s.userId 
          WHERE 
            (COALESCE(e.total_earnings, 0) - COALESCE(s.total_spending, 0)) > 0 
          ORDER BY 
            hollyPoints DESC;
        `);

        weekData = {
          prizeType: shamanPrize.prizeType === 'product' ? 1 : shamanPrize.prizeType === 'hollyPoints' ? 2 : 3,
          endDate: shamanPrize.endDate,
          prizeImage: shamanPrize.prizeImage,
          sponsorImage: shamanPrize.prizeSponsorImage,
          leaderBoard: leaderBoardWeek
        };
      }

      // ***** AYLIK ÖDÜL *****
      const [shamanPrizesMonth] = await pool.query(`
        SELECT * 
        FROM shaman_prize 
        WHERE type = 'month' 
          AND startDate <= CURDATE() 
          AND endDate >= CURDATE() 
        ORDER BY startDate DESC 
        LIMIT 1;
      `);
      let monthData = null;
      if (shamanPrizesMonth.length === 0) {
        monthData = { error: 'Ödül bulunamadı!' };
      } else {
        const shamanPrize = shamanPrizesMonth[0];
        console.log("Aylık Tarih Aralığı:", shamanPrize.startDate, shamanPrize.endDate); // Debugging

        const [leaderBoardMonth] = await pool.query(`
          WITH earnings AS (
            SELECT 
              e.userId, 
              SUM(e.hollyPoints) AS total_earnings 
            FROM 
              holly_points_earning_history e 
              JOIN shaman_prize sp ON e.createdAt BETWEEN sp.startDate AND sp.endDate 
            WHERE 
              sp.type = 'month'
              AND sp.startDate <= CURDATE() 
              AND sp.endDate >= CURDATE()
            GROUP BY 
              e.userId
          ), 
          spending AS (
            SELECT 
              s.userId, 
              SUM(s.hollyPoints) AS total_spending 
            FROM 
              holly_points_spend_history s 
              JOIN shaman_prize sp ON s.createdAt BETWEEN sp.startDate AND sp.endDate 
            WHERE 
              sp.type = 'month'
              AND sp.startDate <= CURDATE() 
              AND sp.endDate >= CURDATE()
            GROUP BY 
              s.userId
          ) 
          SELECT 
            u.id AS userId,
            CONCAT(u.firstName, ' ', u.lastName) AS nameLastName,
            u.levelNumber AS level,
            u.image, 
            u.status,
            COALESCE(e.total_earnings, 0) - COALESCE(s.total_spending, 0) AS hollyPoints 
          FROM 
            users u 
            LEFT JOIN earnings e ON u.id = e.userId 
            LEFT JOIN spending s ON u.id = s.userId 
          WHERE 
            (COALESCE(e.total_earnings, 0) - COALESCE(s.total_spending, 0)) > 0 
          ORDER BY 
            hollyPoints DESC;
        `);

        monthData = {
          prizeType: shamanPrize.prizeType === 'product' ? 1 : shamanPrize.prizeType === 'hollyPoints' ? 2 : 3,
          endDate: shamanPrize.endDate,
          prizeImage: shamanPrize.prizeImage,
          sponsorImage: shamanPrize.prizeSponsorImage,
          leaderBoard: leaderBoardMonth
        };
      }

      // ***** TÜM ZAMAN (ALL-TIME) ÖDÜL *****
      const [shamanPrizesAll] = await pool.query(`
        SELECT * 
        FROM shaman_prize 
        WHERE type = 'all' 
          AND startDate <= CURDATE() 
          AND endDate >= CURDATE() 
        ORDER BY startDate DESC 
        LIMIT 1;
      `);
      let allData = null;
      if (shamanPrizesAll.length === 0) {
        allData = { error: 'Ödül bulunamadı!' };
      } else {
        const shamanPrize = shamanPrizesAll[0];
        console.log("Tüm Zaman Tarih Aralığı:", shamanPrize.startDate, shamanPrize.endDate); // Debugging

        const [leaderBoardAll] = await pool.query(`
          WITH earnings AS (
            SELECT 
              e.userId, 
              SUM(e.hollyPoints) AS total_earnings 
            FROM 
              holly_points_earning_history e 
              JOIN shaman_prize sp ON e.createdAt BETWEEN sp.startDate AND sp.endDate 
            WHERE 
              sp.type = 'all'
              AND sp.startDate <= CURDATE() 
              AND sp.endDate >= CURDATE()
            GROUP BY 
              e.userId
          ), 
          spending AS (
            SELECT 
              s.userId, 
              SUM(s.hollyPoints) AS total_spending 
            FROM 
              holly_points_spend_history s 
              JOIN shaman_prize sp ON s.createdAt BETWEEN sp.startDate AND sp.endDate 
            WHERE 
              sp.type = 'all'
              AND sp.startDate <= CURDATE() 
              AND sp.endDate >= CURDATE()
            GROUP BY 
              s.userId
          ) 
          SELECT 
            u.id AS userId,
            CONCAT(u.firstName, ' ', u.lastName) AS nameLastName,
            u.levelNumber AS level,
            u.image, 
            u.status,
            COALESCE(e.total_earnings, 0) - COALESCE(s.total_spending, 0) AS hollyPoints 
          FROM 
            users u 
            LEFT JOIN earnings e ON u.id = e.userId 
            LEFT JOIN spending s ON u.id = s.userId 
          WHERE 
            (COALESCE(e.total_earnings, 0) - COALESCE(s.total_spending, 0)) > 0 
          ORDER BY 
            hollyPoints DESC;
        `);

        allData = {
          prizeType: shamanPrize.prizeType === 'product' ? 1 : shamanPrize.prizeType === 'hollyPoints' ? 2 : 3,
          endDate: shamanPrize.endDate,
          prizeImage: shamanPrize.prizeImage,
          sponsorImage: shamanPrize.prizeSponsorImage,
          leaderBoard: leaderBoardAll
        };
      }

      // Response içinde tüm veriler
      const data = {
        week: weekData,
        month: monthData,
        all: allData
      };

      return res.status(200).json({ type: 'success', data });
    } catch (err) {
      console.error("Ödül hata:", err);
      res.status(500).json({
        type: 'error',
        error: 'Internal Server Error'
      });
    }
  });




  router.get('/last-winners', verifyJWTToken, async (req, res) => {
    try {
      const prizeTypes = ['week', 'month', 'all'];
      const result = {};

      for (const type of prizeTypes) {
        const [prizes] = await pool.query(`
          SELECT * FROM shaman_prize 
          WHERE type = ? AND winnerId IS NOT NULL AND endDate < CURDATE()
          ORDER BY endDate DESC LIMIT 1;
        `, [type]);

        if (prizes.length === 0) {
          result[type] = { error: 'Henüz kazanılmış bir ödül yok.' };
          continue;
        }

        const prize = prizes[0];

        const [winnerUser] = await pool.query('SELECT * FROM users WHERE id = ?', [prize.winnerId]);

        if (winnerUser.length === 0) {
          result[type] = { error: 'Kazanan kullanıcı bulunamadı.' };
          continue;
        }

        result[type] = {
          prize: {
            id: prize.id,
            type: prize.type,
            prize: prize.prize,
            prizeType: prize.prizeType,
            prizeImage: prize.prizeImage,
            sponsorImage: prize.prizeSponsorImage,
            startDate: prize.startDate,
            endDate: prize.endDate,
            delivery: prize.delivery,
          },
          winner: winnerUser[0]
        };
      }

      return res.status(200).json({ type: 'success', data: result });

    } catch (err) {
      console.error("Son Kazananlar API hata:", err);
      res.status(500).json({
        type: 'error',
        error: 'Internal Server Error'
      });
    }
  });

    return router;
};
