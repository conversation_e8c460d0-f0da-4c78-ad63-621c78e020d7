const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');
const winston = require('winston');
const moment = require('moment');
const axios = require('axios');


const logger = winston.createLogger({
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/error.log' })
    ]
})

module.exports = (pool) => {
  router.get('/', verifyJWTToken, async (req, res) => {
    const userId = req.decodedToken.userId;
  
    try {
      // Türkiye saati (UTC+3) kontrolü
      const now = moment().utcOffset('+03:00');
      const hour = now.hour();
  
      // Saat 06:00 - 18:00 arası chat kapalı
       if (hour >= 6 && hour < 18) {
         return res.status(200).json({
           type: 'success',
           userId,
           userStatusMessage: 'Chat şu an kapalı! Chat 18:00 - 06:00 saatleri arasında aktiftir.',
           activeUsers: [{ message: 'Chat şu an kapalı! Chat 18:00 - 06:00 saatleri arasında aktiftir.' }],
           friends: [],
           requestCount: 0,
         });
       }
  
      // Kullanıcının aktiflik kontrolü
      const [currentUser] = await pool.query(
        'SELECT chatdate FROM users WHERE id = ?',
        [userId]
      );
  
      let userStatusMessage = '';
      if (currentUser.length > 0 && currentUser[0].chatdate) {
        const chatdate = moment(currentUser[0].chatdate);
        const nowMoment = moment();
        const diffHours = nowMoment.diff(chatdate, 'hours');
  
        if (diffHours >= 12) {
          userStatusMessage = 'Üyeleri Görebilmek İçin QR Okutunuz.';
        }
      } else {
        userStatusMessage = 'Üyeleri Görebilmek İçin QR Okutunuz.';
      }
  
      // Aktif kullanıcılar sorgusu
      let activeUsersFormatted = [];
      if (userStatusMessage === 'Üyeleri Görebilmek İçin QR Okutunuz.') {
        activeUsersFormatted = [{ message: 'Üyeleri Görebilmek İçin QR Okutunuz.' }];
      } else {
        const [activeUsers] = await pool.query(
          `SELECT
            u.id AS userId,
            MAX(hf.id) AS lastHfId,
            u.firstName,
            u.lastName,
            u.image,
            u.mood,
            CASE WHEN hf.status = 'accepted' THEN 1 ELSE 0 END AS isFriend
          FROM users u
          LEFT JOIN holly_chat_friends hf ON
            (hf.user1Id = u.id OR hf.user2Id = u.id) AND (hf.user1Id = ? OR hf.user2Id = ?)
          WHERE u.id != ?
            AND u.chatdate >= NOW() - INTERVAL 1 DAY
          GROUP BY u.id, u.firstName, u.lastName, u.image, u.mood, isFriend;`,
          [userId, userId, userId]
        );
  
        activeUsersFormatted = activeUsers.length === 0
          ? [{ message: 'Henüz aktif üye yok' }]
          : activeUsers.map(user => ({ ...user, isFriend: user.isFriend === 1 }));
      }
  
      // Arkadaşlar sorgusu
      const [friends] = await pool.query(
        `SELECT 
          hf.id, 
          u.id as userId, 
          u.firstName, 
          u.lastName, 
          u.image, 
          u.mood, 
          true as isFriend 
        FROM holly_chat_friends hf
        JOIN users u 
          ON (hf.user1Id = ? AND hf.user2Id = u.id) 
          OR (hf.user2Id = ? AND hf.user1Id = u.id)
        WHERE (hf.user1Id = ? OR hf.user2Id = ?)
          AND hf.status = ?;`,
        [userId, userId, userId, userId, 'accepted']
      );
  


  
      // Arkadaş istekleri sorgusu
      const [friendRequests] = await pool.query(
        `SELECT COUNT(u.id) as requestCount 
        FROM holly_chat_friends hf 
        JOIN users u ON (hf.user1Id = ? AND hf.user2Id = u.id) OR (hf.user2Id = ? AND hf.user1Id = u.id) 
        WHERE (hf.user2Id = ? AND hf.status = ?) 
          AND u.chatdate >= NOW() - INTERVAL 1 DAY 
          AND u.id NOT IN (
            SELECT CASE 
              WHEN ((hf2.user1Id = ? OR hf2.user2Id = ?) AND hf2.status = ?) 
              THEN CASE WHEN hf2.user1Id = ? THEN hf2.user2Id ELSE hf2.user1Id END ELSE 0 END 
            FROM holly_chat_friends hf2 
            WHERE (hf2.user1Id = ? OR hf2.user2Id = ?)
          )`,
        [userId, userId, userId, 'pending', userId, userId, 'blocked', userId, userId, userId, userId]
      );
  
      const requestCount = friendRequests[0].requestCount;
  
      // Hediyeleri çek
      const [giftCards] = await pool.query(
        `SELECT gc.id, gc.code, gc.type, gc.value, 0 as giftType 
        FROM gift_cards gc 
        WHERE gc.userId = ? 
          AND gc.giftStatus = 'available'`,
        [userId]
      );
  
      const [tickets] = await pool.query(
        `SELECT ut.id, c.name, c.date, 1 as giftType
        FROM user_tickets ut
        INNER JOIN concert_tickets ct ON ut.ticketId = ct.id
        INNER JOIN concerts c ON ct.concertId = c.id
        WHERE ut.userId = ?
          AND ut.giftStatus = 'available'
          AND ut.payment = 1`,
        [userId]
      );
  
      const mergedResults = [...giftCards, ...tickets];
  
      // Konversasyonlar
      const [chatRooms] = await pool.query(
        `SELECT * FROM holly_chat_rooms 
         WHERE senderId = ? OR receiverId = ?`,
        [userId, userId]
      );
  
      

            // Arkadaşlar listesini oluştur
const formattedFriends = await Promise.all(
  friends.map(async (friend) => {
    const [snaps] = await pool.query(
      `SELECT sp.id, sp.image,
        CASE WHEN fs.postId IS NOT NULL THEN 'true' ELSE 'false' END AS seen
      FROM holly_snap_posts sp
      LEFT JOIN holly_snap_friend_seen fs ON fs.postId = sp.id AND fs.userId = ?
      WHERE sp.userId = ?
        AND sp.createdAt >= NOW() - INTERVAL 1 DAY`, // Son 24 saate ait snapler
      [userId, friend.userId]
  );

      
      return {
          ...friend,
          snaps,
      };
  })
);
  
      // Gelen "sent" mesajları "delivered" olarak güncelleyelim
      await pool.query(
        `UPDATE holly_chat_messages
         SET messageStatus = 'delivered'
         WHERE receiverId = ? AND messageStatus = 'sent'`,
        [userId]
      );
  
      return res.status(200).json({
        type: 'success',
        userId,
        userStatusMessage,
        activeUsers: activeUsersFormatted,
        friends: formattedFriends,
        requestCount,
      });
    } catch (err) {
      console.error(err);
      logger.error(err);
      res.status(500).json({ type: 'error', error: 'Internal Server Error' });
    }
  });



  const moment = require('moment');
  
  router.get('/conversations', verifyJWTToken, async (req, res) => {
    const userId = req.decodedToken.userId;
    
    try {
      // 1) Sohbet odalarını çek
      const [rooms] = await pool.query(
        `SELECT
            r.id AS chatRoomId,
            r.senderId,
            r.receiverId,
            r.updatedAt,
            u_sender.firstName AS senderFirstName,
            u_sender.lastName AS senderLastName,
            u_sender.image AS senderImage,
            u_receiver.firstName AS receiverFirstName,
            u_receiver.lastName AS receiverLastName,
            u_receiver.image AS receiverImage
          FROM holly_chat_rooms r
          JOIN users u_sender ON u_sender.id = r.senderId
          JOIN users u_receiver ON u_receiver.id = r.receiverId
          WHERE r.senderId = ? OR r.receiverId = ?
          ORDER BY r.updatedAt DESC`,
        [userId, userId]
      );
      
      // 2) Sohbetleri Promise.all ile dön
      const conversations = await Promise.all(
        rooms.map(async (room) => {
          // Odaya ait mesajları al
          const [msgs] = await pool.query(
            `SELECT id, senderId, messageContent, createdAt, messageStatus, senderDeleted, receiverDeleted
              FROM holly_chat_messages
              WHERE chatRoomId = ?
              ORDER BY createdAt DESC`, // GiftedChat için en son mesajlar önce
            [room.chatRoomId]
          );
          
          // 3) "Partner" (Karşı Taraf) bilgisini belirle
          let partner = {};
          if (room.senderId === userId) {
            partner = {
              _id: room.receiverId.toString(),
              name: `${room.receiverFirstName} ${room.receiverLastName}`,
              avatar: room.receiverImage,
            };
          } else {
            partner = {
              _id: room.senderId.toString(),
              name: `${room.senderFirstName} ${room.senderLastName}`,
              avatar: room.senderImage,
            };
          }
          
          // 4) Mesajları GiftedChat formatına dönüştür
          const formattedMessages = msgs.map((msg) => {
            // Mesajı gönderen kişi kim? Ona göre isim/avatar seçimi
            let senderName = '';
            let senderAvatar = '';
            
            if (msg.senderId === room.senderId) {
              senderName = `${room.senderFirstName} ${room.senderLastName}`;
              senderAvatar = room.senderImage;
            } else {
              senderName = `${room.receiverFirstName} ${room.receiverLastName}`;
              senderAvatar = room.receiverImage;
            }
            
            // GiftedChat için durum bayraklarını ayarla
            let sent = false;
            let received = false;
            let pending = false;
            
            switch(msg.messageStatus) {
              case 'pending':
                pending = true;
                break;
              case 'sent':
                sent = true;
                break;
              case 'delivered':
                sent = true;
                received = true;
                break;
              case 'read':
                sent = true;
                received = true;
                break;
              default:
                sent = true; // Varsayılan olarak gönderildi kabul et
            }
            
            return {
              _id: msg.id.toString(),
              // Eğer mesaj silinmişse içeriği "Mesaj silindi" yap
              text: (msg.senderDeleted || msg.receiverDeleted) ? "Mesaj silindi" : msg.messageContent,
              createdAt: msg.createdAt,
              user: {
                _id: msg.senderId.toString(),
                name: senderName,
                avatar: senderAvatar,
              },
              // GiftedChat için gerekli durum bayrakları
              sent: sent,
              received: received,
              pending: pending,
              // Özel alanlar
              messageStatus: msg.messageStatus, // Orijinal durum bilgisini de sakla
            };
          });
          
          // 5) Sohbet bilgisini döndür
          return {
            _id: room.chatRoomId.toString(),
            partner,
            messages: formattedMessages,
            updatedAt: room.updatedAt ? moment(room.updatedAt).toISOString() : null,
          };
        })
      );
      
      return res.status(200).json({ type: "success", conversations });
    } catch (error) {
      console.error("Error fetching conversations:", error);
      return res.status(500).json({ type: "error", error: "Internal Server Error" });
    }
  });
      

    router.post('/delete-conversation', verifyJWTToken, async (req, res) => {
        // Gelen parametre kontrolü
        const { conversationPartnerId } = req.body;
        if (!conversationPartnerId) {
          return res.status(400).json({ type: 'error', error: 'conversationPartnerId zorunludur' });
        }
      
        const userId = req.decodedToken.userId;
      
        try {
          await pool.query(
            `UPDATE holly_chat_messages
             SET senderDeleted = 1
             WHERE senderId = ? AND receiverId = ?`,
            [userId, conversationPartnerId]
          );
      
          return res.status(200).json({ type: 'success', message: 'Konuşma mesajları silindi.' });
        } catch (err) {
          console.error(err);
          logger.error(err);
          return res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
      });
      

    router.post(
        '/delete-message',
        [
          body('messageId').notEmpty().withMessage('Message id zorunludur')
        ],
        verifyJWTToken,
        async (req, res) => {
          // VALIDATE
          const errors = validationResult(req);
          if (!errors.isEmpty()) {
            const errorMessages = errors.array().map(error => error.msg);
            return res.status(200).json({ type: 'error', error: errorMessages.join(', ') });
          }
      
          const { messageId } = req.body;
          const userId = req.decodedToken.userId;
      
          try {
            // İlgili mesajı çekelim
            const [rows] = await pool.query(
              'SELECT senderId, receiverId FROM holly_chat_messages WHERE id = ?',
              [messageId]
            );
            
            if (rows.length === 0) {
              return res.status(200).json({ type: 'error', error: 'Mesaj bulunamadı!' });
            }
            
            const message = rows[0];
            
            // Silme işlemini hangi kullanıcı yapıyorsa ona göre güncelleme yapalım
            if (Number(userId) === Number(message.senderId)) {
              // Gönderen mesajı silmek istiyor
              await pool.query(
                'UPDATE holly_chat_messages SET senderDeleted = ? WHERE id = ?',
                [true, messageId]
              );
            } else if (Number(userId) === Number(message.receiverId)) {
              // Alan mesajı silmek istiyor
              await pool.query(
                'UPDATE holly_chat_messages SET receiverDeleted = ? WHERE id = ?',
                [true, messageId]
              );
            } else {
              return res.status(200).json({ type: 'error', error: 'Bu mesajı silme yetkiniz yok!' });
            }
            
            return res.status(200).json({ type: 'success', message: 'Mesaj silindi.' });
            
          } catch (err) {
            console.error(err);
            logger.error(err); // Eğer winston logger kullanıyorsanız
            return res.status(500).json({
              type: 'error',
              error: 'Internal Server Error'
            });
          }
        }
      );

      router.get('/snap-getir', verifyJWTToken, async (req, res) => {
        const { id } = req.query;
        const userId = req.decodedToken.userId;
    
        if (!id) {
            return res.status(400).json({ type: 'error', error: 'Missing id parameter' });
        }
    
        try {
            let isFriend = false;
    
            // Kullanıcı kendi snap'lerine bakıyorsa isFriend true olacak
            if (userId === Number(id)) {
                isFriend = true;
            } else {
                // Kullanıcılar arasındaki arkadaşlık durumunu kontrol et
                const [friendship] = await pool.query(
                    `SELECT hf.status FROM holly_chat_friends hf
                     WHERE (hf.user1Id = ? AND hf.user2Id = ?) OR (hf.user1Id = ? AND hf.user2Id = ?)`,
                    [userId, id, id, userId]
                );
    
                if (friendship.length > 0 && friendship[0].status === 'accepted') {
                    isFriend = true; // Arkadaşlık durumu 'accepted' ise true
                }
            }
    
            // 📌 **Snap'leri snap sahibinin bilgileriyle getir**
            const [snaps] = await pool.query(
                `SELECT sp.id, sp.image,
                CASE WHEN fs.postId IS NOT NULL THEN 'true' ELSE 'false' END AS seen,
                u.id AS ownerId, u.firstName AS ownerFirstName, u.lastName AS ownerLastName, u.image AS ownerImage
                FROM holly_snap_posts sp
                LEFT JOIN holly_snap_friend_seen fs ON fs.postId = sp.id AND fs.userId = ?
                JOIN users u ON sp.userId = u.id
                WHERE sp.userId = ? AND sp.createdAt > DATE_SUB(NOW(), INTERVAL 24 HOUR)`,
                [userId, id]
            );
    
            const formattedSnaps = snaps.map(snap => ({
                ...snap,
                isFriend: isFriend, // Her snap için isFriend değeri eklendi
                owner: {
                    id: snap.ownerId,
                    firstName: snap.ownerFirstName,
                    lastName: snap.ownerLastName,
                    image: snap.ownerImage
                }
            }));
    
            return res.status(200).json({ type: 'success', snaps: formattedSnaps });
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });
    
    
    
    router.get('/blocked-list', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            const [blockedList] = await pool.query(
                'SELECT hf.id, u.id as userId, u.firstName, u.lastName, u.image, u.mood ' +
                'FROM holly_chat_friends hf ' +
                'JOIN users u ON (hf.user1Id = ? AND hf.user2Id = u.id) OR (hf.user2Id = ? AND hf.user1Id = u.id) ' +
                'WHERE hf.user1Id = ? AND hf.status = ?',
                [userId, userId, userId, 'blocked']
            );

            return res.status(200).json({ type: 'success', blockedList });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/remove-block',
        [
            body('id').notEmpty().withMessage('Id zorunludur')
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { id } = req.body;

            try {
                const [blockedList] = await pool.query(
                    'SELECT id ' +
                    'FROM holly_chat_friends ' +
                    'WHERE user1Id = ? AND id = ?',
                    [userId, id]
                );
                if (blockedList.length == 0) {
                    return res.status(200).json({ type: 'error', error: "Engel bulunamadı!" });
                }

                await pool.query(
                    'DELETE FROM holly_chat_friends ' +
                    'WHERE user1Id = ? AND id = ?',
                    [userId, id]
                );

                return res.status(200).json({ type: 'success' });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    router.post('/remove-friend',
        [
            body('id').notEmpty().withMessage('Id zorunludur')
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { id } = req.body;

            try {
                await pool.query(
                    'DELETE FROM holly_chat_friends ' +
                    'WHERE ((user1Id = ? AND user2Id = ?) OR (user1Id = ? AND user2Id = ?))' +
                    'AND NOT (status = ? AND user1Id = ? AND user2Id = ?)',
                    [userId, id, id, userId, 'blocked', id, userId]
                );

                return res.status(200).json({ type: 'success' });
            } catch (err) {
                console.error(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    router.post('/block',
        [
            body('id').notEmpty().withMessage('Id zorunludur')
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { id } = req.body;

            try {
                await pool.query(
                    'DELETE FROM holly_chat_friends ' +
                    'WHERE ((user1Id = ? AND user2Id = ?) OR (user1Id = ? AND user2Id = ?))' +
                    'AND NOT (status = ? AND user1Id = ? AND user2Id = ?)',
                    [userId, id, id, userId, 'blocked', id, userId]
                );

                await pool.query(
                    'INSERT INTO holly_chat_friends ' +
                    'SET user1Id = ?, user2Id = ?, status = ? ',
                    [userId, id, 'blocked']
                );

                return res.status(200).json({ type: 'success' });
            } catch (err) {
                console.error(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    router.get('/request-list', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            const [requestList] = await pool.query(
                'SELECT hf.id, u.id as userId, u.firstName, u.lastName, u.image, u.mood ' +
                'FROM holly_chat_friends hf ' +
                'JOIN users u ON (hf.user1Id = ? AND hf.user2Id = u.id) OR (hf.user2Id = ? AND hf.user1Id = u.id) ' +
                'WHERE (hf.user2Id = ? AND hf.status = ?) AND u.id NOT IN (' +
                'SELECT CASE WHEN ((hf2.user1Id = ? OR hf2.user2Id = ?) AND hf2.status = ?) THEN CASE WHEN hf2.user1Id = ? THEN hf2.user2Id ELSE hf2.user1Id END ELSE 0 END FROM holly_chat_friends hf2 WHERE (hf2.user1Id = ? OR hf2.user2Id = ?)' +
                ')',
                [userId, userId, userId, 'pending', userId, userId, 'blocked', userId, userId, userId]
            );

            return res.status(200).json({ type: 'success', requestList });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/start-conversation', async (req, res) => {
      try {
        const { currentUser, friendId } = req.body;
        const [rooms] = await pool.query(
          `SELECT * FROM holly_chat_rooms 
           WHERE (senderId = ? AND receiverId = ?) OR (senderId = ? AND receiverId = ?)`,
          [currentUser, friendId, friendId, currentUser]
        );
        if (rooms.length > 0) {
          return res.json({ type: "success", chatRoom: rooms[0] });
        }
        const [insertResult] = await pool.query(
          `INSERT INTO holly_chat_rooms (senderId, receiverId, createdAt, updatedAt)
           VALUES (?, ?, NOW(), NOW())`,
          [currentUser, friendId]
        );
        const chatRoomId = insertResult.insertId;
        const [newRoom] = await pool.query(
          "SELECT * FROM holly_chat_rooms WHERE id = ?",
          [chatRoomId]
        );
        return res.json({ type: "success", chatRoom: newRoom[0] });
      } catch (err) {
        console.error(err);
        res.json({ type: "error", error: err.message });
      }
    });

    router.post('/respond-to-request',
        [
            body('id').notEmpty().withMessage('Id zorunludur'),
            body('response').notEmpty().withMessage('Cevap zorunludur')
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { id, response } = req.body;

            if (response !== 'accepted' && response !== 'rejected') {
                return res.status(200).json({ type: 'error', error: "Cevap türü geçersiz!" });
            }

            try {
                const [requestList] = await pool.query(
                    'SELECT id ' +
                    'FROM holly_chat_friends ' +
                    'WHERE user2Id = ? AND id = ?',
                    [userId, id]
                );
                if (requestList.length == 0) {
                    return res.status(200).json({ type: 'error', error: "İstek bulunamadı!" });
                }

                await pool.query(
                    'UPDATE holly_chat_friends ' +
                    'SET status = ? ' +
                    'WHERE user2Id = ? AND id = ?',
                    [response, userId, id]
                );

                return res.status(200).json({ type: 'success' });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    router.post('/add-friend',
        [
            body('id').notEmpty().withMessage('Id zorunludur')
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { id } = req.body;

            try {
                const [requestList] = await pool.query(
                    'SELECT id ' +
                    'FROM holly_chat_friends ' +
                    'WHERE user1Id = ? AND user2Id = ? AND status = ?',
                    [id, userId, 'pending']
                );

                if (requestList.length > 0) {
                    const request = requestList[0];
                    await pool.query(
                        'UPDATE holly_chat_friends ' +
                        'SET status = ? ' +
                        'WHERE user2Id = ? AND id = ?',
                        ['accepted', userId, request.id]
                    );
                } else {
                    await pool.query(
                        'DELETE FROM holly_chat_friends ' +
                        'WHERE ((user1Id = ? AND user2Id = ?) OR (user1Id = ? AND user2Id = ?))' +
                        'AND NOT (status = ? AND user1Id = ? AND user2Id = ?)',
                        [userId, id, id, userId, 'blocked', id, userId]
                    );
                    await pool.query(
                        'INSERT INTO holly_chat_friends ' +
                        'SET user1Id = ?, user2Id = ?, status = ? ',
                        [userId, id, 'pending']
                    );

                    try {
                      // 1. Gönderenin bilgilerini al
                      const [sender] = await pool.query(
                          'SELECT firstName, lastName FROM users WHERE id = ?',
                          [userId]
                      );
                      
                      // 2. Alıcının player_id'sini al
                      const [receiver] = await pool.query(
                          'SELECT player_id FROM users WHERE id = ?',
                          [id]
                      );
  
                      if (receiver[0]?.player_id) {
                          const senderName = `${sender[0].firstName} ${sender[0].lastName}`;
                          const notificationContent = `${senderName} sana arkadaşlık isteği gönderdi!`;
                          
                          await sendNotification(
                              receiver[0].player_id,
                              notificationContent
                          );
                      }
                  } catch (notificationError) {
                      console.error('Bildirim hatası:', notificationError);
                  }

                }

                

                return res.status(200).json({ type: 'success' });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

        async function sendNotification(playerId, content) {
          try {
              console.log(`📢 Bildirim gönderiliyor: ${playerId} - ${content}`);
      
              if (!process.env.ONESIGNAL_APP_ID || !process.env.ONESIGNAL_API_KEY) {
                  console.error("❌ OneSignal API bilgileri eksik!");
                  return;
              }
      
              const url = 'https://onesignal.com/api/v1/notifications';
              const data = {
                  app_id: process.env.ONESIGNAL_APP_ID,
                  include_player_ids: [playerId],
                  contents: { en: content },
                  headings: { en: `Yeni arkadaşlık isteğin var` },
                  data: { 
                      type: 'new_message'
                  },
                  ios_badgeType: 'Increase',
                  ios_badgeCount: 1
              };
      
              const response = await axios.post(url, data, {
                  headers: {
                      'Authorization': `Basic ${process.env.ONESIGNAL_API_KEY}`,
                      'Content-Type': 'application/json'
                  }
              });
      
              console.log(`✅ Bildirim başarılı: ${response.status} - ${response.data.id}`);
          } catch (error) {
              console.error("❌ Bildirim gönderilemedi:", error.response?.data || error.message);
          }
      }
        
          
 
    router.post('/seen-post',
        [
            body('id').notEmpty().withMessage('Id zorunludur')
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { id } = req.body;
            let message = "";

            try {
                const [posts] = await pool.query(
                    `
                    SELECT
                    CASE
                        WHEN fs.postId IS NOT NULL THEN 'true'
                        ELSE 'false'
                    END AS seen
                    FROM holly_snap_posts sp
                    LEFT JOIN holly_snap_friend_seen fs ON fs.postId = sp.id AND fs.userId = ?
                    WHERE sp.id = ?
                    `,
                    [userId, id]
                );

                if (posts.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Post bulunamadı!' });
                } else {
                    const post = posts[0];

                    if (post.seen === 'false') {
                        await pool.query(
                            `
                            INSERT INTO holly_snap_friend_seen
                            SET userId = ?, postId = ?
                            `,
                            [userId, id]
                        );

                        message = 'Görüldü olarak işaretlendi!';
                    } else {
                        message = 'Daha önce görüldü olarak işaretlenmiş!';
                    }
                }

                return res.status(200).json({ type: 'success', message });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    return router;
};