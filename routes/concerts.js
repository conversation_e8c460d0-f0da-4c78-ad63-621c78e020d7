const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');
const formatDate = require('../components/dateFormatter');
const payment = require('../components/payment');
const dayjs = require('dayjs');
const microtime = require('microtime');

module.exports = (pool) => {
    router.get('/', verifyJWTToken, async (req, res) => {
        try {
            const query = `
                SELECT
                    c.*, s.name as stageName,
                    CASE
                        WHEN EXISTS (SELECT 1 FROM concert_tickets ct WHERE ct.concertId = c.id AND ct.hollyPoints IS NOT NULL) THEN TRUE
                        ELSE FALSE
                    END AS hollyPoints
                FROM concerts c
                JOIN stages s ON s.id = c.stageId;
            `;

            const [concerts] = await pool.query(query);

            const formattedResults = concerts.map(concert => ({
                name: concert.name,
                description: concert.description,
                image: concert.image,
                stage: concert.stage,
                date: formatDate(concert.date),
                time: concert.time,
                gateDate: formatDate(concert.gateDate),
                gateTime: concert.gateTime,
                stageName: concert.stageName,
                hollyPoints: concert.hollyPoints === 1 ? true : false
            }));

            return res.status(200).json({ type: 'success', data: formattedResults });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/detail', verifyJWTToken, async (req, res) => {
        const { id } = req.query;
        if (!id) {
            return res.status(200).json({ type: 'error', error: 'Konser bulunamadı!' });
        }
        try {
            const [concerts] = await pool.query(
                `
            SELECT c.*, s.name as stageName
            FROM concerts c
            JOIN stages s ON s.id = c.stageId
            WHERE c.id = ?
            `, [id]
            );
            if (concerts.length == 0) {
                return res.status(200).json({ type: 'error', error: 'Konser bulunamadı!' });
            }

            const [tickets] = await pool.query(
                `
                SELECT 
                ct.*, "ticket" as model, c.image,
                CASE WHEN ct.quota IS NOT NULL 
                AND ct.quota != 0 THEN ct.quota - IFNULL(
                    (
                    SELECT 
                        COUNT(*) 
                    FROM 
                        user_tickets 
                    WHERE 
                        ticketId = ct.id
                    ), 
                    0
                ) ELSE NULL END AS remaining_quota 
                FROM 
                concert_tickets ct 
                JOIN concerts c ON c.id = ct.concertId
                WHERE 
                ct.concertId = ? 
                AND ct.isDeleted = false 
                ORDER BY 
                ct.type DESC, 
                ct.price DESC 
                LIMIT 
                2;
                `, [id]);

            return res.status(200).json({ type: 'success', concert: concerts[0], tickets });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/tickets', verifyJWTToken, async (req, res) => {
        const { id } = req.query;
        if (!id) {
            return res.status(200).json({ type: 'error', error: 'Konser bulunamadı!' });
        }
        try {
            const [tickets] = await pool.query("SELECT ct.*, CASE WHEN ct.quota IS NOT NULL AND ct.quota != 0 THEN ct.quota - IFNULL( (SELECT COUNT(*) FROM user_tickets WHERE ticketId = ct.id), 0 ) ELSE NULL END AS remaining_quota FROM concert_tickets ct WHERE ct.concertId = ? AND ct.isDeleted = false ORDER BY ct.type DESC, ct.price DESC LIMIT 2", [id]);
            if (tickets.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Bilet bulunamadı!' });
            }

            const formattedResults = tickets.map(ticket => ({
                id: ticket.id,
                title: ticket.id,
                type: ticket.type,
                quota: ticket.remaining_quota,
                price: ticket.price,
                hollyPoints: ticket.hollyPoints
            }));
            return res.status(200).json({ type: 'success', data: formattedResults });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/fast-pay',
        [
            body('ticketId').notEmpty(),
            body('quantity').notEmpty(),
            body('ctoken').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const user_ip = req.header('x-forwarded-for');
            const merchantOid = "TKTM" + microtime.now();

            const { ticketId, quantity, ctoken } = req.body;
            try {
                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const user = users[0];

                // -- CHECK ADDRESSES -- //
                const [userAddresses] = await pool.query('SELECT * FROM user_addresses WHERE userId = ?', [userId]);
                if (userAddresses.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Adres bulunamadı!' });
                }
                const userAddress = userAddresses[0]

                // -- CHECK TICKET -- //
                const [tickets] = await pool.query('SELECT ct.*, c.name, c.date FROM concert_tickets ct INNER JOIN concerts c ON ct.concertId = c.id WHERE ct.id = ?', [ticketId]);
                if (tickets.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Adres bulunamadı!' });
                }
                const ticket = tickets[0]

                const basket = [ticket.name, ticket.price, quantity]
                const totalPrice = (ticket.price * quantity).toFixed(2);
                let taxExcludingPrice = (ticket.price - (ticket.price * (ticket.taxRate / 100))).toFixed(2);

                const paymentResult = await payment.getpayment(`${user.firstName} ${user.lastName}`, userAddress.fullAddress, user.phoneNumber, user.email, totalPrice, basket, user_ip, user.utoken, ctoken, merchantOid);
                if (paymentResult.type == "success") {
                    for (let i = 0; i < quantity; i++) {
                        let innerQuantity = 1;
                        let price = ticket.price;
                        let bondedTo = null;
                        if (ticket.type == 1) {
                            innerQuantity = 2;
                            price /= 2;
                            taxExcludingPrice /= 2;
                        }
                        for (let j = 0; j < innerQuantity; j++) {
                            if (bondedTo != null) bondedTo == null;
                            const insertResult = await pool.query('INSERT INTO user_tickets SET userId = ?, ticketId = ?, bondedTo = ?, paidPrice = ?, taxExcludingPrice = ?, qrCode = ?, status = ?, payment = ?, merchantOid = ?',
                                [userId, ticketId, bondedTo, price, taxExcludingPrice, "qrCode", 0, false, merchantOid]);

                            const insertId = insertResult[0].insertId;
                            if (product.type == 1) bondedTo = insertId;
                            const qrCode = insertId + "HLYSTN" + microtime.now() + "T" + dayjs(ticket.date).format("DDMMYYYY");

                            await pool.query('UPDATE user_tickets SET qrCode = ? WHERE id = ?',
                                [qrCode, insertId]);
                        }
                    }

                    // -- HANDLE SUCCESS -- //
                    return res.status(200).json({ type: 'success', paymentLink: paymentResult.paymentLink });
                } else {
                    // -- HANDLE PAYMENT ERROR -- //
                    return res.status(200).json({ type: 'error', message: 'Ödeme hatası' });
                }
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
    });

    router.post('/fast-pay-local', verifyJWTToken, async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map(err => err.msg);
            return res.status(400).json({ type: 'error', error: errorMessages.join(', ') });
        }
    
        const userId = req.decodedToken.userId;
        const user_ip = req.header('x-forwarded-for') || req.connection.remoteAddress;
        const merchantOid = "TKTM" + microtime.now();
    
        // Alınan veriler
        const { ticketId, quantity, cardNumber, expiryMonth, expiryYear, cvv } = req.body;
    
        try {
            // Kullanıcı kontrolü
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(404).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }
            const user = users[0];
    
            // Adres kontrolü (ödeme işleminden önce kes)
            const [userAddresses] = await pool.query('SELECT * FROM user_addresses WHERE userId = ?', [userId]);
            if (userAddresses.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Adres bulunamadı!' });
            }
            const userAddress = userAddresses[0]; // İlk adresi kullan
    
            // Bilet kontrolü (ödeme işleminden önce kes)
            const [tickets] = await pool.query(
                'SELECT ct.*, c.name, c.date FROM concert_tickets ct INNER JOIN concerts c ON ct.concertId = c.id WHERE ct.id = ?',
                [ticketId]
            );
            if (tickets.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Bilet bulunamadı!' });
            }
            const ticket = tickets[0];
    
            // Sepet hazırlığı
            const basket = [[ticket.name, Math.round(ticket.price * 100), quantity]];
            const totalPrice = Math.round(ticket.price * quantity * 100);
            let taxExcludingPrice = (ticket.price - (ticket.price * (ticket.taxRate / 100))).toFixed(2);
    
            // Ödeme parametreleri
            const paymentParams = {
                user_name: `${user.firstName} ${user.lastName}`,
                user_address: userAddress.fullAddress,
                user_phone: user.phoneNumber,
                email: user.email,
                payment_amount: totalPrice,
                basket,
                user_ip,
                merchantOid,
            };
    
            // Kart parametreleri
            const cardParams = {
                card_number: cardNumber.replace(/\s/g, ''),
                expiry_month: expiryMonth,
                expiry_year: expiryYear,
                cvv: cvv,
                utoken: "", // Kayıtlı kart kullanmıyorsanız boş
                ctoken: "",
            };
    
            // Ödeme işlemini gerçekleştir
            const result = await payment.processPaymentFlow(paymentParams, cardParams);
            if (result.type === "success") {
                // Bilet oluşturma işlemi
                for (let i = 0; i < quantity; i++) {
                    let innerQuantity = 1;
                    let price = ticket.price;
                    let bondedTo = null;
    
                    if (ticket.type === 1) { // Çift bilet kontrolü
                        innerQuantity = 2;
                        price /= 2;
                        taxExcludingPrice = (taxExcludingPrice / 2).toFixed(2);
                    }
    
                    for (let j = 0; j < innerQuantity; j++) {
                        if (bondedTo !== null) bondedTo = null; // İlk bilet için bondedTo null
                        const insertResult = await pool.query(
                            'INSERT INTO user_tickets SET userId = ?, ticketId = ?, bondedTo = ?, paidPrice = ?, taxExcludingPrice = ?, qrCode = ?, status = ?, payment = ?, merchantOid = ?',
                            [userId, ticketId, bondedTo, price, taxExcludingPrice, "qrCode", 0, false, merchantOid]
                        );
    
                        const insertId = insertResult[0].insertId;
                        if (ticket.type === 1) bondedTo = insertId; // Çift biletse bir sonrakiyle bağla
    
                        // QR Kod oluşturma
                        const qrCode = insertId + "HLYSTN" + microtime.now() + "T" + dayjs(ticket.date).format("DDMMYYYY");
                        await pool.query('UPDATE user_tickets SET qrCode = ? WHERE id = ?', [qrCode, insertId]);
                    }
                }
    
                // Başarılı yanıt
                return res.status(200).json({ type: "success", paymentLink: result.paymentLink });
            } else {
                return res.status(400).json({ type: "error", error: result.error });
            }
        } catch (err) {
            console.log(err);
            return res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
    });
      

    return router;
};
