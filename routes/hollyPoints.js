const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');

// Holly Points kazanma kaynakları için enum değerleri
const EarnFromTypes = {
    HOLLY_TICKET: 1,
    HOLLY_PAY: 2,
    HOLLY_SNAP: 3,
    HOLLY_SHOP: 4,
    SHAMAN: 5,
    CARK: 6
};

// Holly Points harcama türleri için enum değerleri
const SpendToTypes = {
    WALLET_TRANSFER: 0,
    HOLLY_TICKET: 1,
    HOLLY_SHOP: 2,
    REAL_MONEY: 3,
    GIFT_CARD: 4,
    CARK: 5,
    SHAMAN: 6,
    DISCOUNT: 7
};

// Enum değerlerini dışa aktar
module.exports.EarnFromTypes = EarnFromTypes;
module.exports.SpendToTypes = SpendToTypes;

module.exports = (pool) => {
    router.get('/', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            // -- CHECK USER -- //
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }
            const user = users[0];
            
            // Kullanıcının şehir bilgisini al
            const [vendors] = await pool.query('SELECT * FROM vendors WHERE cityId = ?', [user.cityId]);
            const currentVendorId = vendors.length > 0 ? vendors[0].id : null;
            
            // Şehir bazlı Holly Points sorgularını yap
            const [earningHistoryHollyTicket] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ? AND vendorId = ?", [EarnFromTypes.HOLLY_TICKET, userId, currentVendorId]);
            const [earningHistoryHollyPay] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ? AND vendorId = ?", [EarnFromTypes.HOLLY_PAY, userId, currentVendorId]);
            const [earningHistoryHollySnap] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ? AND vendorId = ?", [EarnFromTypes.HOLLY_SNAP, userId, currentVendorId]);
            const [earningHistoryHollyShop] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ? AND vendorId = ?", [EarnFromTypes.HOLLY_SHOP, userId, currentVendorId]);
            const [earningHistoryShaman] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ? AND vendorId = ?", [EarnFromTypes.SHAMAN, userId, currentVendorId]);
            const [earningHistoryCark] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE earnFrom = ? AND userId = ? AND vendorId = ?", [EarnFromTypes.CARK, userId, currentVendorId]);
            const [spendHistoryHollyTicket] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_spend_history WHERE spendTo = ? AND userId = ? AND vendorId = ?", [SpendToTypes.HOLLY_TICKET, userId, currentVendorId]);
            const [spendHistoryHollyShop] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_spend_history WHERE spendTo = ? AND userId = ? AND vendorId = ?", [SpendToTypes.HOLLY_SHOP, userId, currentVendorId]);
            const [spendHistoryHollyRealMoney] = await pool.query("SELECT SUM(hollyPoints) as total FROM holly_points_spend_history WHERE spendTo = ? AND userId = ? AND vendorId = ?", [SpendToTypes.REAL_MONEY, userId, currentVendorId]);
            
            // Şehir bazlı toplam Holly Points hesapla
            const [totalCityPoints] = await pool.query(
                "SELECT SUM(h.hollyPoints) - COALESCE((SELECT SUM(s.hollyPoints) FROM holly_points_spend_history s WHERE s.userId = ? AND s.vendorId = ?), 0) as total " +
                "FROM holly_points_earning_history h WHERE h.userId = ? AND h.vendorId = ?", 
                [userId, currentVendorId, userId, currentVendorId]
            );
            
            // Kullanıcının tüm şehirlerdeki puan bakiyelerini hesapla
            const [allVendors] = await pool.query('SELECT id, cityId FROM vendors WHERE isDeleted = 0');
            const cityPointsMap = {};
            
            for (const vendor of allVendors) {
                const [vendorPoints] = await pool.query(
                    "SELECT SUM(h.hollyPoints) - COALESCE((SELECT SUM(s.hollyPoints) FROM holly_points_spend_history s WHERE s.userId = ? AND s.vendorId = ?), 0) as total " +
                    "FROM holly_points_earning_history h WHERE h.userId = ? AND h.vendorId = ?", 
                    [userId, vendor.id, userId, vendor.id]
                );
                
                const points = vendorPoints[0].total || 0;
                if (points > 0) {
                    cityPointsMap[vendor.id] = points;
                }
            }
            
            // cityHollyPoints JSON alanını güncelle
            await pool.query('UPDATE users SET cityHollyPoints = ? WHERE id = ?', [JSON.stringify(cityPointsMap), userId]);

            formattedResults = {
                hollyPoints: totalCityPoints[0].total ?? 0,
                currentCity: {
                    id: user.cityId,
                    vendorId: currentVendorId
                },
                earnings: {
                    hollyTicket: earningHistoryHollyTicket[0].total ?? 0,
                    hollyPay: earningHistoryHollyPay[0].total ?? 0,
                    hollySnap: earningHistoryHollySnap[0].total ?? 0,
                    hollyShop: earningHistoryHollyShop[0].total ?? 0,
                    shaman: earningHistoryShaman[0].total ?? 0,
                    cark: earningHistoryCark[0].total ?? 0,

                },
                spendings: {
                    hollyTicket: spendHistoryHollyTicket[0].total ?? 0,
                    hollyShop: spendHistoryHollyShop[0].total ?? 0,
                    realMoney: spendHistoryHollyRealMoney[0].total ?? 0
                }
            }

            return res.status(200).json({ type: 'success', data: formattedResults });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/transferHollyPoints',
        [
            body('prizeId').notEmpty().withMessage('Prize ID zorunludur!'),
            body('prizeType').notEmpty().withMessage('Prize Type zorunludur!')
        ], verifyJWTToken, async (req, res) => {

        console.log('transferHollyPoints endpoint çağrıldı');
        console.log('Request body:', req.body);

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map((error) => error.msg);
            console.log('Validasyon hatası:', errorMessages);
            return res.status(200).json({ type: 'error', error: errorMessages.join(', ') });
        }

        const userId = req.decodedToken.userId;
        const { prizeId, prizeType } = req.body;

        console.log('User ID:', userId);
        console.log('Prize ID:', prizeId);
        console.log('Prize Type:', prizeType);

        try {
            console.log('1. Adım: Kullanıcı kontrolü başlıyor');
            // Kullanıcıyı kontrol et
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            console.log('Kullanıcı sorgusu sonucu:', users.length > 0 ? 'Kullanıcı bulundu' : 'Kullanıcı bulunamadı');

            if (users.length === 0) {
                console.log('Hata: Kullanıcı bulunamadı!');
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }

            console.log('2. Adım: Ödül kontrolü başlıyor');

            let prize;
            let pointValue;
            let tableName;

            // Ödül tipine göre farklı tablolarda sorgulama yap
            if (prizeType === 'wheel') {
                // Çark ödülü için prize_wheel_spin_history tablosunu kontrol et
                const [wheelPrizes] = await pool.query('SELECT * FROM prize_wheel_spin_history WHERE id = ? AND userId = ?', [prizeId, userId]);
                console.log('Wheel ödül sorgusu sonucu:', wheelPrizes.length > 0 ? 'Ödül bulundu' : 'Ödül bulunamadı');

                if (wheelPrizes.length === 0) {
                    console.log('Hata: Ödül bulunamadı!');
                    return res.status(200).json({ type: 'error', error: 'Ödül bulunamadı!' });
                }

                prize = wheelPrizes[0];
                tableName = 'prize_wheel_spin_history';
                pointValue = prize.point_value;

                // Ödül daha önce teslim edilmişse hata döndür
                if (prize.delivery === 1) {
                    console.log('Hata: Bu ödül daha önce zaten teslim edilmiş!');
                    return res.status(200).json({ type: 'error', error: 'Bu ödül daha önce zaten teslim edilmiş!' });
                }

                // Eğer point_value null ise, prize JSON'dan değeri çıkarmayı dene
                if (pointValue === null || pointValue === undefined) {
                    console.log('point_value null, JSON içinden değer çıkarılıyor...');
                    try {
                        // prize alanı JSON string olarak saklanıyor olabilir
                        const prizeData = typeof prize.prize === 'string' ? JSON.parse(prize.prize) : prize.prize;
                        console.log('Parse edilen prize JSON:', prizeData);

                        // JSON içinde value alanı varsa
                        if (prizeData && prizeData.value) {
                            pointValue = parseInt(prizeData.value);
                            console.log('JSON value alanından çıkarılan point değeri:', pointValue);
                        }
                        // JSON içinde name alanında "50 Holly Puan" gibi bir değer olabilir
                        else if (prizeData && prizeData.name) {
                            // Örnek: "50 Holly Puan" -> 50 değerini çıkar
                            const match = prizeData.name.match(/\d+/);
                            if (match) {
                                pointValue = parseInt(match[0], 10);
                                console.log('JSON name alanından çıkarılan point değeri:', pointValue);
                            }
                        }

                        // Eğer hala null ise ve type hollyPoints ise
                        if ((pointValue === null || pointValue === undefined) && prizeData && prizeData.type === 'hollyPoints') {
                            // Varsayılan bir değer ata (50 puan)
                            pointValue = 50;
                            console.log('Varsayılan point değeri atandı:', pointValue);
                        }
                    } catch (jsonError) {
                        console.error('JSON parse hatası:', jsonError);
                    }
                }
            }
            else if (prizeType === 'shaman') {
                // Shaman ödülü için shaman_prize tablosunu kontrol et
                const [shamanPrizes] = await pool.query('SELECT * FROM shaman_prize WHERE id = ? AND winnerId = ?', [prizeId, userId]);
                console.log('Shaman ödül sorgusu sonucu:', shamanPrizes.length > 0 ? 'Ödül bulundu' : 'Ödül bulunamadı');

                if (shamanPrizes.length === 0) {
                    console.log('Hata: Ödül bulunamadı!');
                    return res.status(200).json({ type: 'error', error: 'Ödül bulunamadı!' });
                }

                prize = shamanPrizes[0];
                tableName = 'shaman_prize';

                // Ödül daha önce teslim edilmişse hata döndür
                if (prize.delivery === 1) {
                    console.log('Hata: Bu ödül daha önce zaten teslim edilmiş!');
                    return res.status(200).json({ type: 'error', error: 'Bu ödül daha önce zaten teslim edilmiş!' });
                }

                // Shaman ödülü için value alanını kullan
                pointValue = prize.value;
                console.log('Shaman ödülü value değeri:', pointValue);
            }
            else {
                console.log('Hata: Geçersiz ödül tipi!');
                return res.status(200).json({ type: 'error', error: 'Geçersiz ödül tipi!' });
            }

            console.log('Ödül bilgileri:', prize);

            // Hala null ise hata döndür
            if (pointValue === null || pointValue === undefined) {
                console.log('Hata: Point değeri bulunamadı!');
                return res.status(200).json({ type: 'error', error: 'Point değeri bulunamadı! Yönetici ile iletişime geçin.' });
            }

            console.log('Kullanılacak point değeri:', pointValue);

            console.log('3. Adım: Holly Points kazanım geçmişine ekleniyor');
            try {
                // Kullanıcının şehir bilgisini al
                const [users] = await pool.query('SELECT cityId, cityHollyPoints FROM users WHERE id = ?', [userId]);
                const [vendors] = await pool.query('SELECT id FROM vendors WHERE cityId = ?', [users[0].cityId]);
                const currentVendorId = vendors.length > 0 ? vendors[0].id : null;
                
                // Holly Points kazanım geçmişine ekle (şehir bazlı)
                // Ödül tipine göre earnFrom değerini belirle
                let earnFromValue;
                if (prizeType === 'wheel') {
                    earnFromValue = EarnFromTypes.CARK;
                } else if (prizeType === 'shaman') {
                    earnFromValue = EarnFromTypes.SHAMAN;
                } else {
                    earnFromValue = EarnFromTypes.HOLLY_SNAP; // Varsayılan değer
                }
                
                const [insertResult] = await pool.query(
                    'INSERT INTO holly_points_earning_history (userId, hollyPoints, earnFrom, createdAt, updatedAt, vendorId) VALUES (?, ?, ?, NOW(), NOW(), ?)',
                    [userId, pointValue, earnFromValue, currentVendorId]
                );
                console.log('Kazanım geçmişi eklendi, insert ID:', insertResult.insertId);
                
                // cityHollyPoints JSON alanını güncelle
                let cityPointsMap = {};
                if (users[0].cityHollyPoints) {
                    try {
                        // Eğer string ise JSON.parse et
                        if (typeof users[0].cityHollyPoints === 'string') {
                            cityPointsMap = JSON.parse(users[0].cityHollyPoints);
                        }
                        // Eğer zaten obje ise doğrudan ata
                        else if (typeof users[0].cityHollyPoints === 'object') {
                            cityPointsMap = users[0].cityHollyPoints;
                        }
                    } catch (e) {
                        console.error('JSON parse hatası:', e);
                    }
                }
                
                // Mevcut şehir için puan bakiyesini güncelle
                const currentPoints = cityPointsMap[currentVendorId] || 0;
                cityPointsMap[currentVendorId] = currentPoints + pointValue;
                
                // Veritabanını güncelle
                await pool.query('UPDATE users SET cityHollyPoints = ? WHERE id = ?', 
                    [JSON.stringify(cityPointsMap), userId]);
                console.log('cityHollyPoints güncellendi:', cityPointsMap);
            } catch (insertErr) {
                console.error('Holly Points kazanım geçmişine ekleme hatası:', insertErr);
                throw new Error(`Kazanım geçmişi eklenirken hata: ${insertErr.message}`);
            }

            console.log('4. Adım: Kullanıcının Holly Points değeri güncelleniyor');
            try {
                // Artık global Holly Points değeri güncellemiyoruz, şehir bazlı olarak kaydediyoruz
                // Kullanıcı tablosundaki hollyPoints alanını güncellemiyoruz
                console.log('Kullanıcı Holly Points şehir bazlı olarak kaydedildi');
            } catch (updateErr) {
                console.error('Kullanıcı Holly Points güncelleme hatası:', updateErr);
                throw new Error(`Kullanıcı Holly Points güncellenirken hata: ${updateErr.message}`);
            }

            console.log('5. Adım: Ödülün teslim edildiği işaretleniyor');
            try {
                // Ödülün teslim edildiğini işaretle - tabloya göre farklı sorgular
                if (tableName === 'prize_wheel_spin_history') {
                    const [markResult] = await pool.query(
                        'UPDATE prize_wheel_spin_history SET delivery = 1 WHERE id = ? AND userId = ?',
                        [prizeId, userId]
                    );
                    console.log('Ödül teslim edildi olarak işaretlendi, etkilenen satır:', markResult.affectedRows);
                } else if (tableName === 'shaman_prize') {
                    const [markResult] = await pool.query(
                        'UPDATE shaman_prize SET delivery = 1 WHERE id = ? AND winnerId = ?',
                        [prizeId, userId]
                    );
                    console.log('Ödül teslim edildi olarak işaretlendi, etkilenen satır:', markResult.affectedRows);
                }
            } catch (markErr) {
                console.error('Ödül teslim işaretleme hatası:', markErr);
                throw new Error(`Ödül teslim edildi işaretlenirken hata: ${markErr.message}`);
            }

            console.log('İşlem başarıyla tamamlandı');
            return res.status(200).json({ type: 'success', message: 'Holly Points başarıyla aktarıldı ve ödül teslim edildi.' });

        } catch (err) {
            console.error('transferHollyPoints işleminde hata:');
            console.error(err.message);
            console.error(err.stack);
            return res.status(500).json({
                type: 'error',
                error: 'Internal Server Error: ' + err.message
            });
        }
    });


    // Holly Points'leri indirim koduna dönüştürme
    router.post('/create-discount', [
        body('points').isInt({min: 1}).withMessage('Geçerli bir puan değeri girilmelidir!')
    ], verifyJWTToken, async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(200).json({ type: 'error', error: errors.array()[0].msg });
        }

        const userId = req.decodedToken.userId;
        const { points } = req.body;

        try {
            // Kullanıcı kontrolü
            const [users] = await pool.query('SELECT id, cityId, cityHollyPoints FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }
            
            // Kullanıcının şehir bilgisini al
            const [vendors] = await pool.query('SELECT id FROM vendors WHERE cityId = ?', [users[0].cityId]);
            const currentVendorId = vendors.length > 0 ? vendors[0].id : null;
            
            // JSON'dan mevcut bakiyeyi al
            let cityPointsMap = {};

            if (users[0].cityHollyPoints) {
                try {
                    // Eğer string ise JSON.parse et
                    if (typeof users[0].cityHollyPoints === 'string') {
                        cityPointsMap = JSON.parse(users[0].cityHollyPoints);
                    }
                    // Eğer zaten obje ise doğrudan ata
                    else if (typeof users[0].cityHollyPoints === 'object') {
                        cityPointsMap = users[0].cityHollyPoints;
                    }
                } catch (e) {
                    console.error('JSON parse hatası:', e);
                    return res.status(200).json({ type: 'error', error: 'Puan bilgisi alınamadı!' });
                }
            }

            
            // Mevcut şehir için puan bakiyesi
            const currentCityPoints = cityPointsMap[currentVendorId] || 0;

            // Kullanıcının yeterli puanı var mı kontrol et
            if (currentCityPoints < points) {
                return res.status(200).json({ 
                    type: 'error', 
                    error: `Yeterli Holly Points bakiyeniz bulunmamaktadır. Mevcut bakiyeniz: ${currentCityPoints}` 
                });
            }

            // Settings tablosundan Holly Points değerini al
            const [settings] = await pool.query('SELECT hollyPointsValue FROM settings LIMIT 1');
            if (settings.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Sistem ayarları bulunamadı!' });
            }

            const hollyPointsValue = parseFloat(settings[0].hollyPointsValue);
            const discountAmount = points * hollyPointsValue;

            // Benzersiz indirim kodu oluştur
            const generateDiscountCode = () => {
                const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                let code = '';
                for (let i = 0; i < 8; i++) {
                    code += characters.charAt(Math.floor(Math.random() * characters.length));
                }
                return code;
            };

            let discountCode = generateDiscountCode();
            let isCodeUnique = false;

            // Kodun benzersiz olduğundan emin ol
            while (!isCodeUnique) {
                const [existingCodes] = await pool.query(
                    'SELECT id FROM holly_points_discount_codes WHERE discountCode = ?', 
                    [discountCode]
                );
                
                if (existingCodes.length === 0) {
                    isCodeUnique = true;
                } else {
                    discountCode = generateDiscountCode();
                }
            }

            // İndirim kodunu veritabanına kaydet
            const [insertResult] = await pool.query(
                'INSERT INTO holly_points_discount_codes (userId, discountCode, hollyPoints, discountAmount, isUsed, vendorId) VALUES (?, ?, ?, ?, 0, ?)',
                [userId, discountCode, points, discountAmount, currentVendorId]
            );

            // Holly Points harcama geçmişine ekle
            await pool.query(
                'INSERT INTO holly_points_spend_history (userId, hollyPoints, spendTo, createdAt, updatedAt, vendorId) VALUES (?, ?, ?, NOW(), NOW(), ?)',
                [userId, points, SpendToTypes.DISCOUNT, currentVendorId]
            );

            // Kullanıcının puan bakiyesini güncelle
            cityPointsMap[currentVendorId] = currentCityPoints - points;
            await pool.query('UPDATE users SET cityHollyPoints = ? WHERE id = ?', 
                [JSON.stringify(cityPointsMap), userId]);

            return res.status(200).json({
                type: 'success',
                data: {
                    discountCode: discountCode,
                    hollyPoints: points,
                    discountAmount: discountAmount,
                    message: 'İndirim kodu başarıyla oluşturuldu.'
                }
            });

        } catch (err) {
            console.error('İndirim kodu oluşturma hatası:', err);
            return res.status(500).json({
                type: 'error',
                error: 'İndirim kodu oluşturulurken bir hata oluştu.'
            });
        }
    });

    // Kullanıcının harcadığı Holly Points geçmişini getir
    router.get('/spending-history', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;

        try {
            // Kullanıcı kontrolü
            const [users] = await pool.query('SELECT id, cityId, cityHollyPoints FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }
            
            // Kullanıcının şehir bilgisini al
            const [vendors] = await pool.query('SELECT id FROM vendors WHERE cityId = ?', [users[0].cityId]);
            const currentVendorId = vendors.length > 0 ? vendors[0].id : null;
            
            // JSON'dan mevcut bakiyeyi al (hızlı erişim için)
            let cityPointsMap = {};
            if (users[0].cityHollyPoints) {
                try {
                    // Eğer string ise JSON.parse et
                    if (typeof users[0].cityHollyPoints === 'string') {
                        cityPointsMap = JSON.parse(users[0].cityHollyPoints);
                    }
                    // Eğer zaten obje ise doğrudan ata
                    else if (typeof users[0].cityHollyPoints === 'object') {
                        cityPointsMap = users[0].cityHollyPoints;
                    }
                } catch (e) {
                    console.error('JSON parse hatası:', e);
                }
            }
            
            // Mevcut şehir için puan bakiyesi
            const currentCityPoints = cityPointsMap[currentVendorId] || 0;

            // Şehir bazlı harcama geçmişini getir
            const [spendingHistory] = await pool.query(
                `SELECT
                    id,
                    hollyPoints,
                    spendTo,
                    createdAt,
                    vendorId
                FROM holly_points_spend_history
                WHERE userId = ? AND vendorId = ?
                ORDER BY createdAt DESC
                LIMIT 100`,
                [userId, currentVendorId]
            );
            
            // Ayrıca toplam kazanılan puanları da getir (bakiye + harcanan = toplam kazanılan)
            const [totalEarned] = await pool.query(
                "SELECT SUM(hollyPoints) as total FROM holly_points_earning_history WHERE userId = ? AND vendorId = ?", 
                [userId, currentVendorId]
            );

            // Harcama türlerine göre açıklamaları belirle
            const getSpendingTypeDescription = (spendTo) => {
                switch (spendTo) {
                    case SpendToTypes.WALLET_TRANSFER:
                        return 'Cüzdan Aktarımı';
                    case SpendToTypes.HOLLY_TICKET:
                        return 'Holly Ticket';
                    case SpendToTypes.HOLLY_SHOP:
                        return 'Holly Shop';
                    case SpendToTypes.REAL_MONEY:
                        return 'Nakit Çevirme';
                    case SpendToTypes.GIFT_CARD:
                        return 'Hediye Kartı';
                    case SpendToTypes.CARK:
                        return 'Çarkıfelek';
                    case SpendToTypes.SHAMAN:
                        return 'Şaman Ödülü';
                    case SpendToTypes.DISCOUNT:
                        return 'İndirim Kodu';
                    default:
                        return 'Diğer';
                }
            };

            // Verileri formatla
            const formattedHistory = spendingHistory.map(item => ({
                id: item.id,
                amount: item.hollyPoints,
                spendTo: item.spendTo,
                spendType: getSpendingTypeDescription(item.spendTo),
                description: getSpendingTypeDescription(item.spendTo),
                date: item.createdAt
            }));

            // Toplam harcama miktarını hesapla
            const totalSpent = spendingHistory.reduce((total, item) => total + item.hollyPoints, 0);

            // Harcama türlerine göre grupla
            const spendingByType = {};
            spendingHistory.forEach(item => {
                const typeDescription = getSpendingTypeDescription(item.spendTo);
                if (!spendingByType[typeDescription]) {
                    spendingByType[typeDescription] = {
                        total: 0,
                        count: 0
                    };
                }
                spendingByType[typeDescription].total += item.hollyPoints;
                spendingByType[typeDescription].count += 1;
            });

            return res.status(200).json({
                type: 'success',
                data: {
                    currentBalance: currentCityPoints,
                    totalEarned: totalEarned[0].total || 0,
                    totalSpent: totalSpent,
                    spendingHistory: formattedHistory,
                    spendingByType: spendingByType,
                    historyCount: spendingHistory.length
                }
            });

        } catch (err) {
            console.error('Holly Points harcama geçmişi hatası:', err);
            return res.status(500).json({
                type: 'error',
                error: 'Harcama geçmişi alınırken bir hata oluştu.'
            });
        }
    });

    // İndirim kodunu kontrol et
    router.post('/check-discount', [
        body('discountCode').notEmpty().withMessage('İndirim kodu zorunludur!')
    ], async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(200).json({ type: 'error', error: errors.array()[0].msg });
        }

        const { discountCode } = req.body;

        try {
            // İndirim kodunu kontrol et
            const [discountCodes] = await pool.query(
                'SELECT id, userId, hollyPoints, discountAmount, isUsed, createdAt, vendorId FROM holly_points_discount_codes WHERE discountCode = ?', 
                [discountCode]
            );

            if (discountCodes.length === 0) {
                return res.status(200).json({ 
                    type: 'error', 
                    error: 'Geçersiz indirim kodu!' 
                });
            }

            const discountInfo = discountCodes[0];

            // Kod kullanılmış mı kontrol et
            if (discountInfo.isUsed === 1) {
                return res.status(200).json({ 
                    type: 'error', 
                    error: 'Bu indirim kodu daha önce kullanılmış!' 
                });
            }

            // Kullanıcı bilgilerini getir
            const [users] = await pool.query('SELECT id, name, surname FROM users WHERE id = ?', [discountInfo.userId]);
            const userName = users.length > 0 ? `${users[0].name} ${users[0].surname}` : 'Bilinmeyen Kullanıcı';

            return res.status(200).json({
                type: 'success',
                data: {
                    discountCode: discountCode,
                    hollyPoints: discountInfo.hollyPoints,
                    discountAmount: discountInfo.discountAmount,
                    createdAt: discountInfo.createdAt,
                    userName: userName,
                    isValid: true
                }
            });

        } catch (err) {
            console.error('İndirim kodu kontrol hatası:', err);
            return res.status(500).json({
                type: 'error',
                error: 'İndirim kodu kontrol edilirken bir hata oluştu.'
            });
        }
    });

    // İndirim kodunu kullanıldı olarak işaretle
    router.post('/use-discount', [
        body('discountCode').notEmpty().withMessage('İndirim kodu zorunludur!')
    ], async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(200).json({ type: 'error', error: errors.array()[0].msg });
        }

        const { discountCode } = req.body;

        try {
            // İndirim kodunu kontrol et
            const [discountCodes] = await pool.query(
                'SELECT id, userId, hollyPoints, discountAmount, isUsed FROM holly_points_discount_codes WHERE discountCode = ?', 
                [discountCode]
            );

            if (discountCodes.length === 0) {
                return res.status(200).json({ 
                    type: 'error', 
                    error: 'Geçersiz indirim kodu!' 
                });
            }

            const discountInfo = discountCodes[0];

            // Kod kullanılmış mı kontrol et
            if (discountInfo.isUsed === 1) {
                return res.status(200).json({ 
                    type: 'error', 
                    error: 'Bu indirim kodu daha önce kullanılmış!' 
                });
            }

            // Kodu kullanıldı olarak işaretle
            await pool.query(
                'UPDATE holly_points_discount_codes SET isUsed = 1, updatedAt = NOW() WHERE id = ?',
                [discountInfo.id]
            );

            return res.status(200).json({
                type: 'success',
                data: {
                    discountCode: discountCode,
                    hollyPoints: discountInfo.hollyPoints,
                    discountAmount: discountInfo.discountAmount,
                    message: 'İndirim kodu başarıyla kullanıldı.'
                }
            });

        } catch (err) {
            console.error('İndirim kodu kullanma hatası:', err);
            return res.status(500).json({
                type: 'error',
                error: 'İndirim kodu kullanılırken bir hata oluştu.'
            });
        }
    });

    // Kullanıcının oluşturduğu indirim kodlarını getir
    router.get('/my-discounts', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;

        try {
            // Kullanıcı kontrolü
            const [users] = await pool.query('SELECT id, cityId FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }
            
            // Kullanıcının şehir bilgisini al
            const [vendors] = await pool.query('SELECT id FROM vendors WHERE cityId = ?', [users[0].cityId]);
            const currentVendorId = vendors.length > 0 ? vendors[0].id : null;

            // Kullanıcının oluşturduğu indirim kodlarını getir (şehir bazında)
            const [discounts] = await pool.query(
                `SELECT 
                    hpdc.discountCode,
                    hpdc.hollyPoints,
                    hpdc.discountAmount,
                    hpdc.isUsed,
                    hpdc.createdAt,
                    hpdc.updatedAt,
                    v.name as vendorName
                FROM holly_points_discount_codes hpdc
                LEFT JOIN vendors v ON hpdc.vendorId = v.id
                WHERE hpdc.userId = ? AND hpdc.vendorId = ?
                ORDER BY hpdc.createdAt DESC`,
                [userId, currentVendorId]
            );
            
            // İstatistikleri hesapla
            const totalDiscounts = discounts.length;
            const usedDiscounts = discounts.filter(d => d.isUsed === 1).length;
            const unusedDiscounts = totalDiscounts - usedDiscounts;
            const totalHollyPoints = discounts.reduce((sum, d) => sum + d.hollyPoints, 0);
            const totalDiscountAmount = discounts.reduce((sum, d) => sum + parseFloat(d.discountAmount), 0);

            return res.status(200).json({
                type: 'success',
                data: {
                    discounts: discounts.map(discount => ({
                        discountCode: discount.discountCode,
                        hollyPoints: discount.hollyPoints,
                        discountAmount: parseFloat(discount.discountAmount),
                        isUsed: discount.isUsed === 1,
                        createdAt: discount.createdAt,
                        updatedAt: discount.updatedAt,
                        vendorName: discount.vendorName,
                        status: discount.isUsed === 1 ? 'Kullanıldı' : 'Aktif'
                    })),
                    statistics: {
                        totalDiscounts,
                        usedDiscounts,
                        unusedDiscounts,
                        totalHollyPoints,
                        totalDiscountAmount: parseFloat(totalDiscountAmount.toFixed(2))
                    }
                }
            });
            
        } catch (err) {
            console.error('İndirim kodları getirme hatası:', err);
            return res.status(500).json({
                type: 'error',
                error: 'İndirim kodları alınırken bir hata oluştu.'
            });
        }
    });

    return router;
};
