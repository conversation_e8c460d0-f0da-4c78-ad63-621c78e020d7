const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');
const { EarnFromTypes } = require('./hollyPoints');


module.exports = (pool, io) => {
    router.get('/', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
      
        try {
          const [snaps] = await pool.query(
            `
            SELECT hs.id, hs.images, hs.hollyPoints, hs.activityType, hs.activityId, s.id as stageId,
            CASE 
                WHEN hs.activityType = false THEN c.name 
                ELSE da.name 
            END as activityName,
            CASE 
                WHEN hs.activityType = false THEN c.image 
                ELSE da.image 
            END as activityImage,
            s.name as stageName,
            da.dayOfWeek 
            FROM holly_snap hs 
            LEFT JOIN concerts c ON (hs.activityType = false AND c.id = hs.activityId) 
            LEFT JOIN daily_activities da ON (hs.activityType = true AND da.id = hs.activityId) 
            LEFT JOIN stages s ON 
                (hs.activityType = false AND s.id = c.stageId) 
                OR (hs.activityType = true AND s.id = da.stageId) 
            WHERE hs.isDeleted = false;
            `
          );
      
          if (snaps.length === 0) {
            return res.status(200).json({ type: 'success', snaps: [] });
          }
      
          const todayDayOfWeek = new Date().getDay() === 0 ? 7 : new Date().getDay();
      
          const formattedSnaps = await Promise.all(snaps.map(async (snap) => {
            if (snap.activityType === 1 && snap.dayOfWeek !== todayDayOfWeek) {
              return null;
            }
      
            const [snapUsers] = await pool.query(
              `
              SELECT MIN(sp.id) as id, u.image, u.firstName, u.lastName, sp.userId
              FROM holly_snap_posts sp
              JOIN users u ON u.id = sp.userId
              WHERE sp.snapId = ? AND sp.createdAt > DATE_SUB(NOW(), INTERVAL 24 HOUR)
              GROUP BY sp.userId;
              `,
              [snap.id]
            );
      
            const enrichedSnapUsers = await Promise.all(snapUsers.map(async (user) => {
              const [recentSnaps] = await pool.query(
                `
                SELECT sp.id, sp.image, sp.createdAt
                FROM holly_snap_posts sp
                JOIN holly_snap hs ON hs.id = sp.snapId
                WHERE sp.userId = ? AND hs.activityId = ? AND sp.createdAt > DATE_SUB(NOW(), INTERVAL 24 HOUR);
                `,
                [user.userId, snap.activityId]
              );
      
              return {
                ...user,
                isOwner: user.userId === userId,
                recentSnaps: recentSnaps.map(snap => ({
                  id: snap.id,
                  image: snap.image,
                  createdAt: snap.createdAt
                }))
              };
            }));
      
            const isOwner = snapUsers.some(user => user.userId === userId);
      
            return {
              ...snap,
              images: snap.images ? snap.images.split(',') : [],
              snapUsers: enrichedSnapUsers,
              isOwner
            };
          }));
      
          const filteredSnaps = formattedSnaps.filter(snap => snap !== null);
      
          return res.status(200).json({ type: 'success', snaps: filteredSnaps });
        } catch (err) {
          console.error('An error occurred:', err.message);
          return res.status(500).json({
            type: 'error',
            error: 'Internal Server Error'
          });
        }
      });
      
 

    router.post('/snap-getir', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
    
        try {
            // 📌 **Snapleri alırken snap sahibinin adını ve profil resmini de getiriyoruz**
            const [snaps] = await pool.query(
                `
                SELECT sp.id, sp.image, sp.userId,
                CASE
                    WHEN fs.postId IS NOT NULL THEN 'true'
                    ELSE 'false'
                END AS seen,
                u.firstName AS ownerFirstName, u.lastName AS ownerLastName, u.image AS ownerImage
                FROM holly_snap_posts sp
                LEFT JOIN holly_snap_friend_seen fs ON fs.postId = sp.id AND fs.userId = ?
                LEFT JOIN users u ON sp.userId = u.id  -- 🔥 Kullanıcı bilgilerini ekledim
                `,
                [userId]
            );
    
            // 📌 **Arkadaşları al**
            const [friends] = await pool.query(
                `
                SELECT hf.id, u.id as userId, u.firstName, u.lastName, u.image, u.mood, true as isFriend
                FROM holly_chat_friends hf
                JOIN users u ON (hf.user1Id = ? AND hf.user2Id = u.id) OR (hf.user2Id = ? AND hf.user1Id = u.id)
                WHERE (hf.user1Id = ? OR hf.user2Id = ?) AND hf.status = ?
                AND u.id NOT IN (
                    SELECT CASE WHEN ((hf2.user1Id = ? OR hf2.user2Id = ?) AND hf2.status = ?) 
                    THEN CASE WHEN hf2.user1Id = ? THEN hf2.user2Id ELSE hf2.user1Id END ELSE 0 END 
                    FROM holly_chat_friends hf2 
                    WHERE (hf2.user1Id = ? OR hf2.user2Id = ?)
                )
                `,
                [userId, userId, userId, userId, 'accepted', userId, userId, 'blocked', userId, userId, userId]
            );
    
            // 📌 **Snapleri friends bilgisiyle birleştir**
            const formattedSnaps = snaps.map(snap => {
                const isFriend = friends.some(friend => friend.userId === snap.userId);
                return {
                    ...snap,
                    friends: isFriend,
                    owner: {
                        id: snap.userId,
                        firstName: snap.ownerFirstName,
                        lastName: snap.ownerLastName,
                        image: snap.ownerImage
                    }
                };
            });
    
            // 📌 **Mesajların durumunu güncelle**
            await pool.query(
                `
                UPDATE holly_chat_messages
                SET messageStatus = 'delivered'
                WHERE receiverId = ? AND messageStatus = 'sent';                
                `,
                [userId]
            );
    
            return res.status(200).json({ type: 'success', snaps: formattedSnaps });
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });
    
    

    router.get('/user-posts', verifyJWTToken, async (req, res) => {
        const { id } = req.query;
        if (!id) {
            return res.status(200).json({ type: 'error', error: 'Id gönderilmesi zorunludur!' });
        }
        try {
            const [posts] = await pool.query(
                `
                SELECT * FROM holly_snap_posts
                WHERE userId = ?
                `,
                [id]
            );

            return res.status(200).json({ type: 'success', posts });
        } catch (err) {
            console.error(err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/detail', verifyJWTToken, async (req, res) => {
        const { id } = req.query;
        if (!id) {
            return res.status(200).json({ type: 'error', error: 'Id gönderilmesi zorunludur!' });
        }
        try {
            const [snaps] = await pool.query(
                `
                SELECT hs.images, hs.hollyPoints, hs.instagramPoints
                FROM holly_snap hs 
                WHERE hs.id = ?;
                `,
                [id]
            );
            if (snaps.length == 0) {
                return res.status(200).json({ type: 'error', error: "Snap bulunamadı!" });
            }

            const formattedSnap = snaps.map(snap => ({
                ...snap,
                images: snap.images.split(',')
            }))

            return res.status(200).json({ type: 'success', snap: formattedSnap[0] });
        } catch (err) {
            console.error(err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/snap-delete', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        const { id } = req.body;
    
        if (!id) {
            return res.status(200).json({ type: 'error', error: 'Id gönderilmesi zorunludur!' });
        }
    
        try {
            // Snap sahibinin doğru kullanıcı olup olmadığını kontrol et
            const [snap] = await pool.query(
                `SELECT * FROM holly_snap_posts WHERE id = ? AND userId = ?`,
                [id, userId]
            );
    
            if (snap.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Snap bulunamadı veya yetkiniz yok!' });
            }
    
            // Snap siliniyor
            await pool.query(
                `DELETE FROM holly_snap_posts WHERE id = ?`,
                [id]
            );
    
            io.emit('updateData', { message: 'Bir snap silindi', snapId: id });
    
            return res.status(200).json({ type: 'success', message: 'Snap başarıyla silindi.' });
        } catch (err) {
            console.error(err);
            res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
    });
    
    

    router.post('/share-instagram',
        [
            body('id').notEmpty().withMessage('Id zorunludur')
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { id } = req.body;

            try {
                const [postHistory] = await pool.query(
                    `
                    SELECT * 
                    FROM snap_post_history 
                    WHERE userId = ? AND snapId = ? AND type = false
                    `,
                    [userId, id]
                );
                if (postHistory.length == 0) {
                    const [snaps] = await pool.query(
                        `
                        SELECT hs.hollyPoints
                        FROM holly_snap hs 
                        WHERE hs.id = ?;
                        `,
                        [id]
                    );
                    if (snaps.length == 0) {
                        return res.status(200).json({ type: 'error', error: "Snap bulunamadı!" });
                    }
                    const snap = snaps[0];
                    const hollyPoints = snap.hollyPoints;

                    await pool.query(
                        `
                        INSERT INTO snap_post_history 
                        SET userId = ?, snapId = ?, hollyPoints = ?, type = false
                        `,
                        [userId, id, hollyPoints]
                    );

                    await pool.query(
                        `
                        INSERT INTO holly_points_earning_history 
                        SET userId = ?, hollyPoints = ?, earnFrom = ?
                        `,
                        [userId, hollyPoints, EarnFromTypes.HOLLY_SNAP]
                    );

                    await pool.query(
                        `
                        UPDATE users 
                        SET hollyPoints = hollyPoints + ?
                        WHERE id = ?
                        `,
                        [hollyPoints, userId]
                    );
                }

                return res.status(200).json({ type: 'success' });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        })

                router.post('/share-snap',
            [
                body('id').notEmpty().withMessage('Id zorunludur')
            ], verifyJWTToken, async (req, res) => {
                // -- VALIDATE -- //
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    const errorMessages = errors.array().map((error) => error.msg);
                    const errorMessageString = errorMessages.join(', ');
                    return res.status(200).json({ type: 'error', error: errorMessageString });
                }
        
                const userId = req.decodedToken.userId;
                const { id } = req.body;
        
                try {
                    // -- SON 24 SAATTE PAYLAŞIM SAYISINI KONTROL ET -- //
                    const [recentPosts] = await pool.query(
                        `SELECT COUNT(*) AS postCount 
                         FROM holly_snap_posts 
                         WHERE userId = ? AND createdAt >= NOW() - INTERVAL 24 HOUR`,
                        [userId]
                    );
        
                    if (recentPosts[0].postCount >= 3) {
                        return res.status(200).json({ type: 'error', error: "Son 24 saat içinde en fazla 3 snap paylaşabilirsiniz!" });
                    }
        
                    // -- SNAP VAR MI KONTROL ET -- //
                    const [snaps] = await pool.query(
                        `SELECT hs.hollyPoints FROM holly_snap hs WHERE hs.id = ?;`,
                        [id]
                    );
                    if (snaps.length == 0) {
                        return res.status(200).json({ type: 'error', error: "Snap bulunamadı!" });
                    }
                    const snap = snaps[0];
                    const hollyPoints = snap.hollyPoints;
        
                    // -- SNAP SON 24 SAATTE PAYLAŞILMIŞ MI KONTROL ET -- //
                    const [postHistory] = await pool.query(
                        `SELECT * FROM snap_post_history 
                         WHERE userId = ? AND snapId = ? AND type = true 
                         AND createdAt >= NOW() - INTERVAL 24 HOUR`,
                        [userId, id]
                    );
                    const alreadyShared = postHistory.length > 0;
        
                    // -- HOLLY SNAP POST OLUŞTUR -- //
                    const [insertResult] = await pool.query(
                        `INSERT INTO holly_snap_posts (userId, snapId) VALUES (?, ?);`,
                        [userId, id]
                    );
                    const insertId = insertResult.insertId;
        
                    await pool.query(
                        `UPDATE holly_snap_posts SET image = ? WHERE id = ?;`,
                        [`holly_snap_posts/${insertId}.webp`, insertId]
                    );
        
                    // -- SNAP POST HISTORY EKLE -- //
                    await pool.query(
                        `INSERT INTO snap_post_history (userId, snapId, hollyPoints, type) VALUES (?, ?, ?, true);`,
                        [userId, id, alreadyShared ? 0 : hollyPoints] // Daha önce paylaşıldıysa puanı 0 yap
                    );
        
                    if (!alreadyShared) {
                        // -- HOLLY POINTS KAZANDIR -- //
                        await pool.query(
                            `INSERT INTO holly_points_earning_history (userId, hollyPoints, earnFrom) VALUES (?, ?, ?);`,
                            [userId, hollyPoints, EarnFromTypes.HOLLY_SNAP]
                        );
        
                        await pool.query(
                            `UPDATE users SET hollyPoints = hollyPoints + ? WHERE id = ?;`,
                            [hollyPoints, userId]
                        );
                    }

                    io.emit('updateData', { message: 'Yeni bir snap paylaşıldı', snapId: insertId });

        
                    return res.status(200).json({ type: 'success', fileName: `holly_snap_posts/${insertId}` });

        
                } catch (err) {
                    console.error(err);
                    res.status(500).json({ type: 'error', error: 'Internal Server Error' });
                }
            }
        );
        
        
    
    return router;
};