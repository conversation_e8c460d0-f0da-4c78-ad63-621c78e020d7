const express = require('express');
const router = express.Router();
const verifyJWTToken = require('../middleware/verifyJWTToken');
const { body, validationResult } = require('express-validator');
const dayjs = require('dayjs');
const levelComponent = require('../components/level');
const sendSms = require('../components/sendSms');
const sendMail = require('../components/sendMail');

module.exports = (pool) => {
    router.post(
        '/announcements',
        verifyJWTToken,
        async (req, res) => {
            try {
                const { type } = req.body;
                let queryString;
                let queryParams = [];
    
                // -- QUERY DYNAMICALLY BASED ON TYPE -- //
                if (!type) {
                    // Tüm duyuruları getir (createdAt'e göre sıralanmış)
                    queryString = 'SELECT * FROM announcements ORDER BY createdAt DESC';
                } else {
                    // Belirli bir type için duyuruları getir
                    queryString = 'SELECT * FROM announcements WHERE type = ? ORDER BY createdAt DESC';
                    queryParams.push(type);
                }
    
                // -- EXECUTE QUERY -- //
                const [announcements] = await pool.query(queryString, queryParams);
    
                // -- CHECK IF ANNOUNCEMENTS EXIST -- //
                if (announcements.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Duyuru bulunamadı!' });
                }
    
                // -- SUCCESS RESPONSE -- //
                return res.status(200).json({
                    type: 'success',
                    data: announcements.map(announcement => ({
                        ...announcement,
                        type: announcement.type // Type bilgisi her slider içinde dönecek
                    }))
                });
            } catch (err) {
                console.error(err);
                // -- HANDLE ERROR -- //
                return res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error',
                });
            }
        }
    );
    

    router.get('/get-settings', verifyJWTToken, async (req, res) => {
        try {
            const [settings] = await pool.query('SELECT * FROM settings LIMIT 1');
    
            return res.status(200).json({ 
                type: 'success', 
                hollyPointsValue: settings[0].hollyPointsValue, 
                contract: 'Settings retrieved successfully', // Contract bilgisi
            });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });
        

    router.post('/version', [
        body('currentVersion').notEmpty().withMessage('Mevcut versiyon boş olamaz'),
        body('platform').notEmpty().withMessage('Platform bilgisi boş olamaz'),
    ], async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map((error) => error.msg);
            const errorMessageString = errorMessages.join(', ');
            return res.status(200).json({ type: 'error', error: errorMessageString });
        }

        const { currentVersion, platform } = req.body;

        try {
            const [latestVersion] = await pool.query(
                'SELECT * FROM app_versions WHERE platform = ? AND status = ? ORDER BY createdAt DESC LIMIT 1',
                [platform, 'active']
            );

            if (latestVersion.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Versiyon bilgisi bulunamadı!' });
            }

            // Version karşılaştırma fonksiyonu
            const compareVersions = (current, latest) => {
                const currentParts = current.split('.').map(Number);
                const latestParts = latest.split('.').map(Number);

                // Eksik kısımları 0 ile doldur
                const maxLength = Math.max(currentParts.length, latestParts.length);
                while (currentParts.length < maxLength) currentParts.push(0);
                while (latestParts.length < maxLength) latestParts.push(0);

                for (let i = 0; i < maxLength; i++) {
                    if (currentParts[i] < latestParts[i]) return -1; // Güncelleme gerekli
                    if (currentParts[i] > latestParts[i]) return 1;  // Mevcut daha yeni
                }
                return 0; // Aynı versiyon
            };

            const versionComparison = compareVersions(currentVersion, latestVersion[0].version);
            const needsUpdate = versionComparison < 0; // Sadece mevcut versiyon daha eskiyse güncelleme gerekli

            return res.status(200).json({
                type: 'success',
                needsUpdate,
                latestVersion: latestVersion[0].version,
                updateMessage: latestVersion[0].updateMessage,
                updateUrl: latestVersion[0].updateUrl,
                forceUpdate: latestVersion[0].forceUpdate || false
            });
        } catch (err) {
            console.error('Version check error:', err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/open-positions', verifyJWTToken, async (req, res) => {
        try {
            const { cityID } = req.query; // cityID parametresini al

            let query = 'SELECT * FROM open_positions WHERE status = ?';
            let params = [true];

            // Eğer cityID parametresi varsa, sorguya ekle
            if (cityID) {
                query += ' AND cityID = ?';
                params.push(cityID);
                console.log(`Filtering open positions by cityID: ${cityID}`);
            }

            const [openPositions] = await pool.query(query, params);
            console.log(`Found ${openPositions.length} open positions`);

            return res.status(200).json({ type: 'success', openPositions });
        } catch (err) {
            console.error('Error getting open positions:', err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/franchises', verifyJWTToken, async (req, res) => {
        try {
            const [franchises] = await pool.query('SELECT * FROM franchises');

            return res.status(200).json({ type: 'success', franchises });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/apply-franchise', [
        body('franchise').notEmpty().withMessage('Franchise zorunludur'),
        body('nameLastName').notEmpty().withMessage('Ad Soyad zorunludur'),
        body('email').isEmail().withMessage('Geçerli bir email adresi giriniz'),
        body('birthDate').notEmpty().withMessage('Doğum tarihi zorunludur'),
        body('address').notEmpty().withMessage('Adres zorunludur'),
        body('phoneNumber').notEmpty().withMessage('Telefon numarası zorunludur'),
        body('currentJob').notEmpty().withMessage('Şuanki iş zorunludur'),
        body('experience').notEmpty().withMessage('Tecrübe zorunludur'),
        body('q1').notEmpty().withMessage('Soru 1 zorunludur'),
        body('q2').notEmpty().withMessage('Soru 2 zorunludur'),
        body('property').notEmpty().withMessage('Mesken durumu zorunludur'),
        body('areaSize').notEmpty().withMessage('Alan genişliği zorunludur'),
        body('floorCount').notEmpty().withMessage('Kat sayısı zorunludur'),
        body('roofHeight').notEmpty().withMessage('Tavan yüksekliği zorunludur'),
        body('investmentAmount').notEmpty().withMessage('Yatırım miktarı zorunludur'),
    ], async (req, res) => {
        // -- VALIDATE -- //
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const errorMessages = errors.array().map((error) => error.msg);
            const errorMessageString = errorMessages.join(', ');
            return res.status(200).json({ type: 'error', error: errorMessageString });
        }
    
        const { 
            franchise, 
            nameLastName, 
            email, 
            birthDate, 
            address, 
            phoneNumber, 
            landPhoneNumber, 
            currentJob, 
            experience, 
            q1, 
            q2, 
            property, 
            areaSize, 
            floorCount, 
            roofHeight, 
            buildingAddress, 
            investmentAmount 
        } = req.body;
    
        try {
            // Doğrudan başvuruyu ekle - kullanıcı kontrolü yok
            const insertResult = await pool.query(
                `INSERT INTO franchise_applications SET 
                 franchise = ?, 
                 nameLastName = ?, 
                 email = ?, 
                 birthDate = ?, 
                 address = ?, 
                 phoneNumber = ?, 
                 landPhoneNumber = ?, 
                 currentJob = ?, 
                 experience = ?, 
                 q1 = ?, 
                 q2 = ?, 
                 property = ?, 
                 areaSize = ?, 
                 floorCount = ?, 
                 roofHeight = ?, 
                 buildingAddress = ?, 
                 investmentAmount = ?`,
                [
                    franchise, 
                    nameLastName, 
                    email, 
                    birthDate, 
                    address, 
                    phoneNumber, 
                    landPhoneNumber || null, // Opsiyonel
                    currentJob, 
                    experience, 
                    q1, 
                    q2, 
                    property, 
                    areaSize, 
                    floorCount, 
                    roofHeight, 
                    buildingAddress || null, // Opsiyonel
                    investmentAmount
                ]
            );
    
            const insertId = insertResult[0].insertId;
    
            // Görsel path'ini güncelle
            await pool.query('UPDATE franchise_applications SET images = ?', [`franchise/${insertId}.webp`]);
            
            // -- HANDLE SUCCESS -- //
            res.status(200).json({ type: 'success', id: insertId });
            
        } catch (err) {
            console.log(err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/apply-position',
        [
            body('profession').notEmpty().withMessage('Başvuru alanı zorunludur'),
            body('resume').notEmpty().withMessage('Özgeçmiş zorunludur')
                .isLength({ min: 1 }).withMessage('Özgeçmiş en az 25 karakter olmak zorundadır')
                .isLength({ max: 1000 }).withMessage('Özgeçmiş en fazla 1000 karakter olmak zorundadır'),
            body('description').notEmpty().withMessage('Açıklama zorunludur')
                .isLength({ min: 1 }).withMessage('Açıklama en az 25 karakter olmak zorundadır')
                .isLength({ max: 1000 }).withMessage('Açıklama en fazla 1000 karakter olmak zorundadır'),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { resume, profession, description } = req.body;

            try {
                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const user = users[0];

                await pool.query('INSERT INTO job_applications SET cityId = ?, nameLastName = ?, birthDate = ?, phoneNumber = ?, email = ?, resume = ?, profession = ?, description = ?',
                    [user.cityId, `${user.firstName} ${user.lastName}`, user.dateOfBirth, user.phoneNumber, user.email, resume, profession, description]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success' });
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    router.post('/apply-performer',
        [
            body('videos').notEmpty().withMessage('Videolar zorunludur')
                .isLength({ min: 1 }).withMessage('Videolar en az 8 karakter olmak zorundadır')
                .isLength({ max: 1000 }).withMessage('Videolar en fazla 1000 karakter olmak zorundadır'),
            body('description').notEmpty().withMessage('Açıklama zorunludur')
                .isLength({ min: 1 }).withMessage('Açıklama en az 25 karakter olmak zorundadır')
                .isLength({ max: 1000 }).withMessage('Açıklama en fazla 1000 karakter olmak zorundadır'),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const userId = req.decodedToken.userId;
            const { videos, description } = req.body;

            try {
                // -- CHECK USER -- //
                const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
                if (users.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
                }
                const user = users[0];

                await pool.query('INSERT INTO performer_applications SET cityId = ?, nameLastName = ?, phoneNumber = ?, description = ?, videos = ?',
                    [user.cityId, `${user.firstName} ${user.lastName}`, user.phoneNumber, description, videos.trim()]);

                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success' });
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

        router.post('/contact', [
            body('nameLastName').notEmpty().withMessage('Ad Soyad zorunludur')
                .isLength({ min: 2 }).withMessage('Ad Soyad en az 2 karakter olmak zorundadır')
                .isLength({ max: 100 }).withMessage('Ad Soyad en fazla 100 karakter olmak zorundadır'),
            body('email').isEmail().withMessage('Geçerli bir email adresi giriniz'),
            body('phoneNumber').notEmpty().withMessage('Telefon numarası zorunludur')
                .isMobilePhone('tr-TR').withMessage('Geçerli bir telefon numarası giriniz'),
            body('topic').notEmpty().withMessage('Konu zorunludur')
                .isLength({ min: 1 }).withMessage('Konu en az 1 karakter olmak zorundadır')
                .isLength({ max: 255 }).withMessage('Konu en fazla 255 karakter olmak zorundadır'),
            body('message').notEmpty().withMessage('Mesaj zorunludur')
                .isLength({ min: 1 }).withMessage('Mesaj en az 1 karakter olmak zorundadır')
                .isLength({ max: 1000 }).withMessage('Mesaj en fazla 1000 karakter olmak zorundadır'),
        ], async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }
        
            const { nameLastName, email, phoneNumber, cityId, topic, message } = req.body;
        
            try {
                // Doğrudan iletişim başvurusunu ekle - kullanıcı kontrolü yok
                await pool.query(
                    `INSERT INTO contact_applications SET 
                     cityId = ?, 
                     nameLastName = ?, 
                     email = ?, 
                     phoneNumber = ?, 
                     topic = ?, 
                     message = ?`,
                    [
                        cityId || null, // Opsiyonel şehir
                        nameLastName, 
                        email, 
                        phoneNumber, 
                        topic, 
                        message
                    ]
                );
        
                // -- HANDLE SUCCESS -- //
                res.status(200).json({ type: 'success' });
                
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }
        });

    router.get('/cities', verifyJWTToken, async (req, res) => {
        try {
            const [cities] = await pool.query('SELECT * FROM turkey_cities');

            return res.status(200).json({ type: 'success', cities });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/agreements', async (req, res) => {
        try {
          const queryString = 'SELECT id, agreement, text FROM agreements';
  
          const [agreements] = await pool.query(queryString);
  
          if (agreements.length === 0) {
            return res.status(200).json({ type: 'error', error: 'No agreements found' });
          }
  
          return res.status(200).json({ type: 'success', data: agreements });
        } catch (err) {
          console.error(err); 
          res.status(500).json({ type: 'error', error: 'Internal Server Error' });
        }
      });

      router.get('/delete-account', (req, res) => {
        const htmlContent = `
            <html>
                <head>
                    <title>Hesap Silme</title>
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            background-color: #f9f9f9;
                            color: #333;
                            margin: 0;
                            padding: 0;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                        }
                        .container {
                            background: #fff;
                            padding: 20px;
                            border-radius: 10px;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                            max-width: 90%;
                            width: 400px;
                            box-sizing: border-box;
                        }
                        h1 {
                            font-size: 24px;
                            margin-bottom: 20px;
                            color: #444;
                            text-align: center;
                        }
                        p {
                            font-size: 16px;
                            margin-bottom: 20px;
                            text-align: center;
                        }
                        label {
                            display: block;
                            font-size: 14px;
                            margin-bottom: 8px;
                        }
                        input {
                            width: 100%;
                            padding: 10px;
                            margin-bottom: 20px;
                            border: 1px solid #ccc;
                            border-radius: 5px;
                            box-sizing: border-box;
                        }
                        button {
                            width: 100%;
                            padding: 10px;
                            background-color: #007BFF;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                            font-size: 16px;
                        }
                        button:hover {
                            background-color: #0056b3;
                        }
                        a {
                            display: block;
                            text-align: center;
                            margin-top: 20px;
                            color: #007BFF;
                            text-decoration: none;
                            font-size: 14px;
                        }
                        a:hover {
                            text-decoration: underline;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>Hesap Silme</h1>
                        <p>Hesabınızı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.</p>
                        <form method="POST" action="/delete-account-confirm">
                            <label for="phone">Telefon Numaranızı Girin:</label><br>
                            <input type="text" id="phone" name="phone" placeholder="Telefon numaranız" required><br>
                            <button type="submit">Silme Talebini Gönder</button>
                        </form>
                        <a href="/">İptal</a>
                    </div>
                </body>
            </html>
        `;
    
        res.status(200).send(htmlContent);
    });

    router.get('/districts', verifyJWTToken, async (req, res) => {
        const cityKey = req.query.cityKey;

        if (!cityKey) {
            return res.status(200).json({ type: 'success', districts: [] });
        }

        try {
            const [districts] = await pool.query('SELECT * FROM turkey_districts WHERE cityKey = ?', [cityKey]);

            return res.status(200).json({ type: 'success', districts });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/neighborhoods', verifyJWTToken, async (req, res) => {

        const districtKey = req.query.districtKey;

        if (!districtKey) {
            return res.status(200).json({ type: 'success', neighborhoods: [] });
        }

        try {
            const [neighborhoods] = await pool.query('SELECT * FROM turkey_neighborhoods WHERE districtKey = ?', [districtKey]);

            return res.status(200).json({ type: 'success', neighborhoods });
        } catch (err) {
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.get('/get-components', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            const [concerts] = await pool.query('SELECT * FROM concerts ORDER BY createdAt DESC LIMIT 1');
            const concert = concerts.length > 0 ? concerts[0].date : null;
    
            let activeUsersFormatted = [];
    
            const [activeUsers] = await pool.query(`
                SELECT
                    u.id AS userId,
                    MAX(hf.id) AS lastHfId,
                    u.firstName,
                    u.lastName,
                    u.image,
                    u.mood,
                    MAX(CASE WHEN (hf.user1Id = ? OR hf.user2Id = ?) AND hf.status = 'accepted' THEN true ELSE false END) AS isFriend
                FROM
                    users u
                LEFT JOIN holly_chat_friends hf ON
                    hf.user1Id = u.id OR hf.user2Id = u.id
                WHERE
                    u.id != ?
                GROUP BY
                    u.id, u.firstName, u.lastName, u.image, u.mood;
                `,
                [userId, userId, userId]
            );
    
            if (activeUsers.length === 0) {
                activeUsersFormatted = [{ message: 'Henüz aktif üye yok' }];
            } else {
                activeUsersFormatted = activeUsers.map(user => ({
                    ...user,
                    isFriend: user.isFriend === 1 ? true : false
                }));
            }
    
            const [announcements] = await pool.query("SELECT * FROM announcements ORDER BY createdAt DESC");
    
            return res.status(200).json({ type: 'success', concertDate: concert, friends: activeUsersFormatted, announcements });
        } catch (err) {
            console.error(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });
    router.post('/search',
        [
            body('type').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            try {
                const query = `
                    SELECT
                    c.*, s.name as stageName,
                    CASE
                        WHEN EXISTS (SELECT 1 FROM concert_tickets ct WHERE ct.concertId = c.id AND ct.hollyPoints IS NOT NULL) THEN TRUE
                        ELSE FALSE
                    END AS hollyPoints,
                    (
                        SELECT MIN(price) FROM concert_tickets ct WHERE ct.concertId = c.id
                    ) AS price
                FROM concerts c
                JOIN stages s ON s.id = c.stageId;
                `;
                const [concerts] = await pool.query(query);
                const [dailyActivities] = await pool.query(
                    `SELECT da.*, s.name as stageName 
                     FROM daily_activities da 
                     JOIN stages s ON s.id = da.stageId 
                     WHERE da.isDeleted = false 
                     ORDER BY da.dayOfWeek ASC;` // Günlere göre sıralama
                );

                if (concerts.length === 0 && dailyActivities.length === 0) {
                    return res.status(200).json({ type: 'error', error: 'Sonuç bulunamadı!' });
                }

                const mergedResults = [
                    ...concerts.map(concert => ({
                        id: concert.id,
                        name: concert.name,
                        stage: concert.stageId,
                        dateTime: concert.date,
                        gateDateTime: concert.gateDate,
                        image: concert.image,
                        price: concert.price,
                        stageName: concert.stageName,
                        hollyPoints: concert.hollyPoints === 1 ? true : false,
                        type: 1
                    })),
                    ...dailyActivities.map(activity => ({
                        id: activity.id,
                        name: activity.name,
                        stage: activity.stageId,
                        dateTime: activity.date,
                        gateDateTime: activity.gateDate,
                        stageName: activity.stageName,
                        image: activity.image,
                        dayofweek: activity.dayOfWeek,
                        type: 2
                    }))
                ];

                return res.status(200).json({ type: 'success', data: mergedResults });
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }

        });

    router.post('/orderStatus', async (req, res) => {
        res.status(200).json({
            "OrderStatus": [
                {
                    "Id": 1,
                    "Value": "Onaylandı"
                },
                {
                    "Id": 2,
                    "Value": "İptal Edildi"
                }
            ]
        });
    });

    router.post('/paymentMethods', async (req, res) => {
        res.status(200).json({
            "PaymentMethods": [
                {
                    "Id": 1,
                    "Value": "PayTR"
                }
            ]
        });
    });

    router.post('/orders', async (req, res) => {
        const token = req.header('token');

        const { orderStatusId, startDateTime, endDateTime } = req.body;
        const [vendors] = await pool.query('SELECT * FROM vendors WHERE invoiceToken = ? ORDER BY createdAt DESC LIMIT 1', [token]);
        if (vendors.length === 0) {
            return res.status(200).json({ type: 'error', error: 'İşletme bulunamadı!' });
        }
        const vendor = vendors[0];

        const [invoices] = await pool.query('SELECT * FROM invoices WHERE vendorId = ? AND orderStatusId = ? AND orderDate BETWEEN ? AND ? ORDER BY createdAt', [vendor.id, orderStatusId, dayjs(startDateTime).format('YYYY-MM-DD HH:mm:ss'), dayjs(endDateTime).format('YYYY-MM-DD HH:mm:ss')]);
        if (invoices.length === 0) {
            return res.status(200).json({ type: 'error', error: 'Fatura bulunamadı!' });
        }

        const formattedResults = invoices.map(invoice => {
            const orderDetails = JSON.parse(invoice.orderDetails);
            return {
                OrderId: invoice.orderId,
                OrderCode: invoice.orderCode,
                OrderDate: dayjs(invoice.orderDate).format('DD.MM.YYYY HH:mm:ss'),
                CustomerId: invoice.userId,
                BillingName: invoice.billingName,
                BillingAddress: invoice.billingAddress,
                BillingTown: invoice.billingTown,
                BillingMobilePhone: invoice.billingMobilePhone,
                BillingPhone: invoice.billingMobilePhone,
                SSNTCNo: invoice.identityNumber,
                Email: invoice.email,
                PaymentTypeId: 1,
                PaymentType: "PayTR",
                Currency: "TRY",
                CurrencyRate: 1,
                TotalPaidTaxExcluding: invoice.taxExcludingPrice,
                TotalPaidTaxIncluding: invoice.taxIncludingPrice,
                ProductsTotalTaxExcluding: invoice.taxExcludingPrice8,
                ProductsTotalTaxIncluding: invoice.taxIncludingPrice,
                OrderDetails: [orderDetails.map(detail => {
                    const code = `HL${detail.id}T${new Date(detail.createdAt).getFullYear().toString().slice(-2)}${String(new Date(detail.createdAt).getMonth() + 1).padStart(2, '0')}${String(new Date(detail.createdAt).getDate()).padStart(2, '0')}`;
                    return {
                        id: detail.id,
                        code,
                        name: detail.name,
                        image: `https://epic-shortly-minnow.ngrok-free.app/resources/images/${detail.image}`,
                        taxRate: detail.taxRate,
                        price: detail.price,
                        quantity: detail.quantity
                    }
                })]
            }
        });

        return res.status(200).json({ Orders: formattedResults });
    });

    router.post('/ansver-application',
        [
            body('phoneNumber').notEmpty(),
            body('message').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const { phoneNumber, message } = req.body;

            console.log({ phoneNumber, message });

            try {
                result = await sendSms(phoneNumber, message);

                // -- STATUS CONTROL -- //
                const statusCode = result?.response?.status[0]?.code[0];
                const errorMessage = result?.response?.status[0]?.message[0];

                if (statusCode === '200') {
                    // -- SMS SUCCESS -- //
                    return res.status(200).json({ type: "success" });
                } else {
                    return res.status(200).json({ type: "error", error: errorMessage });
                }
            } catch (err) {
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }

        });

    router.post('/send-mail',
        [
            body('id').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const { id } = req.body;

            try {
                const result = await sendMail(pool, id, false);

                if (result.status == true) {
                    return res.status(200).json({ type: "success" });
                } else {
                    return res.status(200).json({ type: "error", error: result.error });
                }

            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }

        });

    router.post('/send-sms',
        [
            body('id').notEmpty(),
        ], verifyJWTToken, async (req, res) => {
            // -- VALIDATE -- //
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorMessages = errors.array().map((error) => error.msg);
                const errorMessageString = errorMessages.join(', ');
                return res.status(200).json({ type: 'error', error: errorMessageString });
            }

            const { id } = req.body;

            try {
                const [ticket] = await pool.query(`
                SELECT cit.title, c.name, c.date, ut.qrCode, u.firstName, u.lastName, u.email, u.phoneNumber
                FROM user_tickets ut
                JOIN users u ON ut.userId = u.id
                JOIN concert_tickets ct ON ut.ticketId = ct.id
                JOIN concerts c ON ct.concertId = c.id
                JOIN vendors v ON c.vendorId = v.id
                JOIN cities cit ON v.cityId = cit.id
                WHERE ut.id = ?;
                `, [id]);

                if (ticket.length === 0) {
                    res.status(200).json({
                        type: 'error',
                        error: 'Bilet bulunamadı'
                    });
                }

                const userTicket = ticket[0];

                const message = `Merhaba ${userTicket.firstName}, Holly Stone dan almış olduğunuz bilet için teşekkür ederiz. Konser biletinize ulaşmak için, https://hollystone.com.tr/siparisdurum.php?benzersiz=${userTicket.qrCode}`;

                result = await sendSms(userTicket.phoneNumber, message);

                console.log({ message, result });

                // -- STATUS CONTROL -- //
                const statusCode = result?.response?.status[0]?.code[0];
                const errorMessage = result?.response?.status[0]?.message[0];

                if (statusCode === '200') {
                    // -- SMS SUCCESS -- //
                    return res.status(200).json({ type: "success" });
                } else {
                    return res.status(200).json({ type: "error", error: errorMessage });
                }
            } catch (err) {
                console.log(err);
                // -- HANDLE ERROR -- //
                return res.status(500).json({
                    type: 'error',
                    error: 'Internal Server Error'
                });
            }

        });

    router.get('/vendors', verifyJWTToken, async (req, res) => {
        try {
            const [vendors] = await pool.query('SELECT id, cityID, name, active, latitude, longitude, modules FROM vendors');

            const formattedVendors = vendors.map(vendor => ({
                ...vendor,
                modules: vendor.modules.split(',')
            }));

            return res.status(200).json({ type: 'success', vendors: formattedVendors });
        } catch (err) {
            // -- HANDLE ERROR -- //
            console.log(err);
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    router.post('/get-modules', verifyJWTToken, async (req, res) => {
        const { cityId } = req.body; // Body'den gelen cityId
    
        if (!cityId) {
            return res.status(400).json({ type: 'error', error: 'Şehir ID gerekli!' });
        }
    
        try {
            // `vendors` tablosundan `cityId` ile modülleri al
            const [vendor] = await pool.query('SELECT modules FROM vendors WHERE cityId = ?', [cityId]);
    
            // Eğer cityId ile ilişkili vendor bulunamazsa hata döndür
            if (vendor.length === 0) {
                return res.status(404).json({ type: 'error', error: 'Bu şehirde vendor bulunamadı!' });
            }
    
            // Modülleri döndür
            const modules = vendor[0].modules.split(','); // Modüller virgülle ayrılmış biçimde tutuluyorsa split() kullanıyoruz
            return res.status(200).json({ type: 'success', modules });
    
        } catch (err) {
            console.error(err);
            return res.status(500).json({ type: 'error', error: 'Sunucu hatası' });
        }
    });
    

    router.get('/level', verifyJWTToken, async (req, res) => {
        const userId = req.decodedToken.userId;
        try {
            const [users] = await pool.query('SELECT * FROM users WHERE id = ?', [userId]);
            if (users.length === 0) {
                return res.status(200).json({ type: 'error', error: 'Kullanıcı bulunamadı!' });
            }
            const user = users[0];
            const levelData = JSON.parse(user.level);
            const userLevel = await levelComponent.findUserLevel(userId, pool);
            let complete = [false, false, false, false, false, false, false, false, false, false];
            levelData.forEach((element, index) => {
                const length = element.filter(target => target.complete).length;
                if (length == 5) {
                    complete[index] = true;
                }
            });

            const hollyPoints = [10, 50, 150, 300, 400, 700, 500, 500, 500, 500]

            return res.status(200).json({ type: 'success', level: userLevel, levelData, hollyPoints, complete });
        } catch (err) {
            console.log(err);
            // -- HANDLE ERROR -- //
            res.status(500).json({
                type: 'error',
                error: 'Internal Server Error'
            });
        }
    });

    return router;
};
