FROM node:18

WORKDIR /usr/src/app

# Uygulama bağımlılıklarını kopyala
COPY package*.json ./

# Sadece production bağımlılıklarını yükle
RUN npm install --omit=dev

# Uygulama kodunu kopyala
COPY . .

# resources dizini için kalıcı bir volume oluştur
VOLUME /usr/src/app/resources

# resources dizinini oluştur ve izinleri ayarla
RUN mkdir -p /usr/src/app/resources && chmod -R 777 /usr/src/app/resources

# Uygulamayı çalıştır
CMD ["node", "app.js"]
