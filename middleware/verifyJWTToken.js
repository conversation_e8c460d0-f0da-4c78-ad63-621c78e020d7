const jwt = require('jsonwebtoken');
require('dotenv').config();
const secretKey = process.env.SECRET_KEY;

const verifyJWTToken = (req, res, next) => {
    const token = req.headers.authorization;
    try {
        if (typeof token !== 'undefined') {
            const bearerToken = token.split(' ')[1];
            const decodedToken = jwt.verify(bearerToken, secretKey);

            if (decodedToken) {
                req.decodedToken = decodedToken;
                next();
            } else {
                return res.status(200).json({ type: 'error', error: 'Oturum süreniz doldu!' });
            }
        } else {
            return res.status(200).json({ type: 'error', error: 'Bearer Token bulunamadı' });
        }
    } catch (error) {
        return res.status(200).json({ type: 'error', error: 'Oturum süreniz doldu!' });
    }
};

module.exports = verifyJWTToken;
