const socketIo = require("socket.io");

let io; // `io` nesnesini burada saklıyoruz

const connectedUsers = {}; // Kullanıcı ID'lerini socket ID ile eşleştirmek için

const inappropriateWords = ["badword1", "badword2", "badword3"]; // Yasaklı kelimeler listesi

module.exports = {
    init: (server) => {
        io = socketIo(server, {
            pingTimeout: 60000,
            cors: { origin: "*" }
        });

        io.on("connection", (socket) => {
            console.log("Connected to socket.io");
        
            // Odaya katılma işlemi
            socket.on("join room", (roomId) => {
                socket.join(roomId);
                console.log(`User ${socket.id} joined room ${roomId}`);
            });
        
            // Kullanıcı yazmaya başladığında
            socket.on('is_typing', ({ roomId, userId }) => {
                socket.to(roomId).emit('user_typing', { userId });
            });
        
            // <PERSON>llanıcı yazmayı durdurduğunda
            socket.on('stop_typing', ({ roomId, userId }) => {
                socket.to(roomId).emit('user_stopped', { userId });
            });
        
            // Yeni mesaj gönderme
            socket.on("new message", async (newMessageReceived) => {
                try {
                    const { senderId, receiverId, text, messageType, parentId, chatRoomId, giftStatus } = newMessageReceived;
        
                    console.log(text)
        
                    // Uygunsuz içerik kontrolü
                    if (messageType !== "gift") {
                        const containsInappropriateContent = inappropriateWords.some(word => text.includes(word));
                        if (containsInappropriateContent) {
                            return socket.emit("message error", "Mesaj içeriği uygunsuz kelimeler içeriyor.");
                        }
                    }
        
                    // Hediye mesajı kontrolü
                    if (messageType === "gift") {
                        let giftData;
                        try {
                          giftData = JSON.parse(text);  // text'i nesneye dönüştürüyoruz
                        } catch (err) {
                          console.error("Gift JSON parse error:", err);
                          return socket.emit("message error", "Geçersiz hediye verisi.");
                        }
                        
                        // Artık giftData.giftType diyerek doğru şekilde erişebiliriz
                        if (giftData.giftType === 1) {
                          // Kullanıcı biletlerini güncelleme
                          const [updateResult] = await pool.query(
                            `UPDATE user_tickets
                             SET bondedTo = ?, giftStatus = 'sent'
                             WHERE id = ? AND userId = ? AND giftStatus = 'available'`,
                            [receiverId, giftData.id, senderId]
                          );
                          if (updateResult.affectedRows === 0) {
                            throw new Error("Bilet güncellenemedi veya zaten gönderildi.");
                          }
                        } else if (giftData.giftType === 0) {
                          // Hediye kartlarını güncelleme
                          const [updateResult] = await pool.query(
                            `UPDATE gift_cards
                             SET bondedTo = ?, giftStatus = 'sent'
                             WHERE id = ? AND userId = ? AND giftStatus = 'available'`,
                            [receiverId, giftData.id, senderId]
                          );
                          if (updateResult.affectedRows === 0) {
                            throw new Error("Hediye kartı güncellenemedi veya zaten gönderildi.");
                          }
                        } else {
                          throw new Error("Geçersiz giftType değeri.");
                        }
                        
                        console.log(
                          `Hediye başarıyla gönderildi. giftType: ${giftData.giftType}, id: ${giftData.id}`
                        );
                      }
        
                    // Mesajı veritabanına kaydet
                    const messageId = await require("./components/messages").sendMessage(
                        require("./app").pool, senderId, receiverId, messageType, text, parentId
                    );        
                    // Mesajı odaya gönder
                    io.to(chatRoomId).emit("message received", { ...newMessageReceived, id: messageId, giftStatus: giftStatus || (messageType === 'gift' ? 'pending' : null) });
        
                } catch (error) {
                    console.error("Error sending message:", error);
                    socket.emit("message error", "Internal Server Error");
                }
            });
        
            // Mesaj silme işlemi
            socket.on("delete message", async ({ messageId }) => {
                try {
                    const result = await require("./components/messages").deleteMessage(
                        require("./app").pool, messageId
                    );
                    if (result) {
                        io.emit("message deleted", { messageId });
                    }
                } catch (error) {
                    console.error("Error deleting message:", error);
                }
            });
        
            // Hediye yanıtı
            socket.on("reply gift", async (giftReply) => {
                try {
                    const res = await require("./components/messages").giftReply(
                        require("./app").pool, giftReply
                    );
                    if (res) {
                        io.emit("gift replied", giftReply);
                    }
                } catch (error) {
                    console.error("Error in gift reply:", error);
                }
            });

            // Snap güncellemesi
            socket.on("updatedData", () => {
                console.log("📸 Yeni Snap paylaşıldı!");
                io.emit("updatedData"); // Tüm istemcilere bildir
            });
        
            // Kullanıcı bağlantıyı kapattığında
            socket.on("disconnect", () => {
                for (const userId in connectedUsers) {
                    if (connectedUsers[userId] === socket.id) {
                        delete connectedUsers[userId];
                        break;
                    }
                }
                console.log("Disconnected from socket.io");
            });
        });

        return io;
    },

    getIo: () => {
        if (!io) {
            throw new Error("❌ Socket.IO başlatılmadı!");
        }
        return io;
    }
};
